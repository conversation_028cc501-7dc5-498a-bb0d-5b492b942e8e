import json
import logging
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse, HttpResponse
from utils.response import APIResponse, get_language_from_request

# Setup logging
logger = logging.getLogger(__name__)


class APIKeyValidationMiddleware(MiddlewareMixin):
    """
    Middleware สำหรับตรวจสอบ API Key ในทุก request ที่ต้องการ authentication
    
    การใช้งาน:
    1. เพิ่ม 'MCDC.middleware.APIKeyValidationMiddleware' ใน MIDDLEWARE ใน settings.py
    2. กำหนด VALID_API_KEYS ใน settings.py (สามารถเป็น string หรือ list)
    3. (Optional) กำหนด API_KEY_VALIDATION_SETTINGS ใน settings.py เพื่อ configure paths
    
    Configuration ใน settings.py:
    ```python
    VALID_API_KEYS = 'your-api-key' หรือ ['key1', 'key2']
    
    API_KEY_VALIDATION_SETTINGS = {
        'ENABLED': True,  # เปิด/ปิดการ validate
        'PROTECTED_PATHS': ['/api/', '/auth/'],  # paths ที่ต้องการ validate
        'EXEMPTED_PATHS': ['/static/', '/admin/'],  # paths ที่ไม่ต้อง validate
    }
    ```
    
    API Key สามารถส่งผ่าน headers:
    - X-API-Key: your-api-key
    - Authorization: Bearer your-api-key
    - Authorization: ApiKey your-api-key
    """
    
    def __init__(self, get_response):
        """
        Initialize middleware with configuration from settings
        """
        super().__init__(get_response)
        
        # Load configuration from settings
        self.config = getattr(settings, 'API_KEY_VALIDATION_SETTINGS', {})
        self.enabled = self.config.get('ENABLED', True)
        self.protected_paths = self.config.get('PROTECTED_PATHS', ['/api/', '/auth/'])
        self.exempted_paths = self.config.get('EXEMPTED_PATHS', [
            '/static/', '/media/', '/admin/', '/swagger/', '/redoc/', '/docs/', '/api/schema/',
            '/api/search/', '/.well-known/'  # Add well-known endpoints as exempted from API key validation
        ])
    
    def process_request(self, request):
        """
        ตรวจสอบ API Key ก่อนที่ request จะไปถึง view
        """
        logger.info(f"API Key Middleware - Processing request: {request.path}")
        
        # ตรวจสอบว่า middleware เปิดใช้งานหรือไม่
        if not self.enabled:
            logger.info("API Key Middleware - Disabled, skipping validation")
            return None
        
        # ตรวจสอบว่าเป็น path ที่ต้องการ validate หรือไม่
        should_validate = self._should_validate_api_key(request.path)
        logger.info(f"API Key Middleware - Should validate path {request.path}: {should_validate}")
        
        if not should_validate:
            logger.info("API Key Middleware - Path exempted, skipping validation")
            return None
        
        # ดึงภาษาจาก request
        language = get_language_from_request(request)
        
        # ดึง API key จาก header
        api_key = self._get_api_key_from_request(request)
        logger.info(f"API Key Middleware - API key found: {api_key is not None}")
        
        if not api_key:
            logger.error(f"API key missing for path: {request.path}")
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=401
            )
        
        # ตรวจสอบ API key
        is_valid = self._validate_api_key(api_key)
        logger.info(f"API Key Middleware - API key valid: {is_valid}")
        
        if not is_valid:
            logger.error(f"Invalid API key for path: {request.path}")
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=401
            )
        
        logger.info(f"API key validation successful for path: {request.path}")
        return None
    
    def _should_validate_api_key(self, path):
        """
        ตรวจสอบว่า path นี้ต้องการ validate API Key หรือไม่
        
        Args:
            path (str): Request path
            
        Returns:
            bool: True หาก path ต้องการ validate API Key
        """
        # ตรวจสอบ exempted paths ก่อน
        for exempted_path in self.exempted_paths:
            if path.startswith(exempted_path):
                return False
        
        # ตรวจสอบ protected paths
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True
        
        return False
    
    def _get_api_key_from_request(self, request):
        """
        ดึง API key จาก request headers
        
        Args:
            request: Django request object
            
        Returns:
            str or None: API key หากพบ, None หากไม่พบ
        """
        # ลองดึงจาก X-API-Key header ก่อน
        api_key = request.headers.get('X-API-Key')
        if api_key:
            logger.info(f"API Key Middleware - Found API key in X-API-Key header")
            return api_key
        
        # ลองดึงจาก Authorization header เฉพาะกรณีที่ไม่ใช่ JWT token
        auth_header = request.headers.get('Authorization')
        logger.info(f"API Key Middleware - Authorization header: {auth_header}")
        
        if auth_header:
            # ถ้าเป็น Bearer token (JWT) ให้ข้าม เพราะไม่ใช่ API key
            if auth_header.startswith('Bearer ') and '.' in auth_header:
                # นี่น่าจะเป็น JWT token ไม่ใช่ API key
                logger.info(f"API Key Middleware - Found JWT token in Authorization header, skipping API key validation")
                return None
            
            # ถ้าเป็น ApiKey format ให้ดึงออกมา
            if auth_header.startswith('ApiKey '):
                logger.info(f"API Key Middleware - Found API key in Authorization header with ApiKey prefix")
                return auth_header.replace('ApiKey ', '')
        
        logger.info(f"API Key Middleware - No API key found in headers")
        return None
    
    def _validate_api_key(self, api_key):
        """
        ตรวจสอบความถูกต้องของ API key
        
        Args:
            api_key (str): API key ที่ต้องการตรวจสอบ
            
        Returns:
            bool: True หาก API key ถูกต้อง
        """
        try:
            # ดึง valid API keys จาก settings
            valid_api_keys_setting = getattr(settings, 'VALID_API_KEYS', ['mcdc-api-key-2024'])
            
            # รองรับทั้ง string และ list
            if isinstance(valid_api_keys_setting, str):
                valid_api_keys = [valid_api_keys_setting]
            else:
                valid_api_keys = valid_api_keys_setting
            
            # ลบ prefix และตรวจสอบ
            clean_api_key = api_key.replace('Bearer ', '').replace('ApiKey ', '')
            
            return clean_api_key in valid_api_keys
            
        except Exception as e:
            logger.error(f"Error validating API key: {str(e)}")
            return False


class APIVersionMiddleware(MiddlewareMixin):
    """
    Middleware สำหรับเพิ่ม API_VERSION เข้าไปในทุกๆ response
    """
    
    def process_response(self, request, response):
        """
        เพิ่ม API_VERSION เข้าไปใน response
        """
        # ตรวจสอบว่าเป็น API request หรือไม่
        if (request.path.startswith('/api/') or 
            request.path.startswith('/auth/') or 
            request.path.startswith('/swagger/') or
            request.path.startswith('/redoc/')):
            
            # ข้าม static files และ admin
            if (request.path.startswith('/static/') or 
                request.path.startswith('/admin/') or
                request.path.startswith('/media/')):
                return response
            
            # ตรวจสอบว่าเป็น JSON response หรือไม่
            content_type = response.get('Content-Type', '')
            
            if 'application/json' in content_type:
                try:
                    # Parse JSON content
                    if response.content:
                        data = json.loads(response.content.decode('utf-8'))
                        
                        # เพิ่ม API_VERSION เข้าไปใน response เฉพาะ dict objects
                        if isinstance(data, dict):
                            data['api_version'] = getattr(settings, 'API_VERSION', 'v.0.0.1')
                            
                            # สร้าง response ใหม่
                            response.content = json.dumps(
                                data, 
                                ensure_ascii=False, 
                                separators=(',', ':')  # compact JSON
                            ).encode('utf-8')
                            
                            # อัพเดท Content-Length
                            response['Content-Length'] = len(response.content)
                            
                except (json.JSONDecodeError, ValueError, UnicodeDecodeError) as e:
                    # หากไม่สามารถ parse JSON ได้ ก็ไม่ต้องทำอะไร
                    pass
        
        return response 


class JWTUserMiddleware(MiddlewareMixin):
    """
    Middleware สำหรับตรวจสอบ JWT token และเซ็ตค่า user attribute

    ทำงานก่อน AuthenticationMiddleware เพื่อให้แน่ใจว่าเราตรวจสอบและเซ็ตค่า user ได้ถูกต้อง
    """

    # Paths that don't require JWT authentication (public endpoints)
    PUBLIC_PATHS = [
        '/api/search/',  # All search endpoints are public
        '/static/',
        '/media/',
        '/files/',
        '/admin/',
        '/swagger/',
        '/redoc/',
        '/docs/',
        '/api/schema/',
        '/api/docs/',
        '/.well-known/',  # Well-known endpoints for iOS Universal Links
    ]

    def _should_skip_jwt_auth(self, path):
        """
        ตรวจสอบว่า path นี้ควรข้าม JWT authentication หรือไม่
        """
        return any(path.startswith(public_path) for public_path in self.PUBLIC_PATHS)

    def process_request(self, request):
        """
        ตรวจสอบ JWT token และเซ็ตค่า user attribute
        """
        from authentication.backends import CustomJWTAuthentication
        from django.contrib.auth.models import AnonymousUser

        # ตรวจสอบว่าเป็น public path หรือไม่
        if self._should_skip_jwt_auth(request.path):
            logger.info(f"JWT User Middleware - Skipping JWT auth for public path: {request.path}")
            return None

        # ตรวจสอบว่ามี Authorization header หรือไม่
        auth_header = request.headers.get('Authorization')

        if auth_header and auth_header.startswith('Bearer '):
            # มี Bearer token ให้ลองใช้ custom authentication
            try:
                jwt_auth = CustomJWTAuthentication()
                user_auth = jwt_auth.authenticate(request)

                if user_auth:
                    user, token = user_auth

                    # Store the original user object directly on request with our custom attribute
                    # This way Django's AuthenticationMiddleware won't completely override it
                    request.user = user

                    # Save the original authenticated user and token as attributes on the request
                    request._jwt_authenticated_user = user
                    request._jwt_auth_token = token

                    # Add special attribute to help identify this user as JWT authenticated
                    setattr(request, '_jwt_user_authenticated', True)

                    logger.info(f"JWT User Middleware - User authenticated: {user.username}")
                    logger.info(f"JWT User Middleware - User type: {getattr(user, 'user_type', 'unknown')}")
                    logger.info(f"JWT User Middleware - Is authenticated: {user.is_authenticated}")

            except Exception as e:
                logger.error(f"JWT User Middleware - Error authenticating user: {str(e)}")

        return None
        
    def process_response(self, request, response):
        """
        Restore the JWT authenticated user if it was overwritten
        """
        # If we saved a JWT authenticated user but the current user is anonymous,
        # restore our authenticated user
        if hasattr(request, '_jwt_authenticated_user') and (
            not hasattr(request, 'user') or 
            getattr(request.user, 'is_authenticated', False) is not True
        ):
            request.user = request._jwt_authenticated_user
            logger.info(f"JWT User Middleware - Restored JWT authenticated user: {request.user.username}")
        
        return response 