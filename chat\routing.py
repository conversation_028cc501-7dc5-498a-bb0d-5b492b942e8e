# -*- coding: utf-8 -*-
"""
WebSocket routing for chat application
"""
from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Mobile chat WebSocket endpoints
    re_path(r'ws/chat/room/(?P<room_id>[\w_]+)/$', consumers.MobileChatConsumer.as_asgi()),
    re_path(r'ws/chat/session/(?P<session_id>[\w_]+)/$', consumers.ChatSessionConsumer.as_asgi()),

    # Legacy endpoints (if needed)
    re_path(r'ws/chat/(?P<room_name>\w+)/$', consumers.ChatConsumer.as_asgi()),
    re_path(r'ws/chat/private/(?P<user_id>\d+)/$', consumers.PrivateChatConsumer.as_asgi()),
    re_path(r'ws/chat/consult/(?P<consult_id>\d+)/$', consumers.ConsultChatConsumer.as_asgi()),
]
