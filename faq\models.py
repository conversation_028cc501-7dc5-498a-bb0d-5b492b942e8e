from django.db import models


class TcdAppFaqcategory(models.Model):
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    order = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_app_faqcategory'


class TcdAppFaq(models.Model):
    question_th = models.TextField(db_collation='Thai_CI_AI')
    question_en = models.TextField(db_collation='Thai_CI_AI')
    answer_th = models.TextField(db_collation='Thai_CI_AI')
    answer_en = models.TextField(db_collation='Thai_CI_AI')
    app_faqcategory = models.ForeignKey(TcdAppFaqcategory, on_delete=models.PROTECT)
    is_app = models.BooleanField()
    is_web = models.BooleanField()
    create_user = models.IntegerField()
    create_date = models.DateTimeField()
    update_date = models.DateTimeField(blank=True, null=True)
    status = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_app_faq'
