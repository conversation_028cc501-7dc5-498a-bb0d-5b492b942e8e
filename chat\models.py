from django.db import models
from MCDC.models import TcdUsers
from authentication.models import TcdUserConsult


class TcdChat(models.Model):
    type = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    message = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    file_name = models.CharField(max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    file_src = models.CharField(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    user_consult = models.ForeignKey(TcdUserConsult, on_delete=models.CASCADE, null=True, blank=True)
    users = models.ForeignKey(TcdUsers, on_delete=models.CASCADE, null=True, blank=True)
    consult_read = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    users_read = models.Char<PERSON><PERSON>(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    date = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'tcd_chat'


class TcdChatPrompt(models.Model):
    question_th = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    question_en = models.CharField(max_length=250, db_collation='Thai_CI_AI')
    answer_th = models.TextField(db_collation='Thai_CI_AI')
    answer_en = models.TextField(db_collation='Thai_CI_AI')
    status = models.BooleanField()
    create_user_id = models.IntegerField()
    create_date = models.DateTimeField()
    update_user_id = models.IntegerField(blank=True, null=True)
    update_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_chat_prompt'


class TcdSettingChat(models.Model):
    start_time = models.TimeField()
    end_time = models.TimeField()
    detail_th = models.CharField(max_length=500, db_collation='Thai_CI_AI')
    detail_en = models.CharField(max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField()
    is_message_limited = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_setting_chat'
