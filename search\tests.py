from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch, MagicMock
from decimal import Decimal

from .services import ProjectInterestService
from .models import TcdAppProjectConsult


class ProjectInterestServiceTest(TestCase):
    """
    Test cases for ProjectInterestService
    """

    def setUp(self):
        """
        Set up test data
        """
        self.app_project_id = 123
        self.user_consult_id = 456
        self.app_member_id = 789

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    def test_get_project_interest_status_success(self, mock_filter):
        """
        Test successful retrieval of project interest status
        """
        # Mock project consult object
        mock_project_consult = MagicMock()
        mock_project_consult.id = 1
        mock_project_consult.app_project_id = Decimal(self.app_project_id)
        mock_project_consult.user_consult_id = Decimal(self.user_consult_id)
        mock_project_consult.app_member_id = Decimal(self.app_member_id)
        mock_project_consult.consult_send = 1

        mock_filter.return_value.first.return_value = mock_project_consult

        # Call the service
        result = ProjectInterestService.get_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['id'], 1)
        self.assertEqual(result['data']['app_project_id'], self.app_project_id)
        self.assertEqual(result['data']['user_consult_id'], self.user_consult_id)
        self.assertEqual(result['data']['app_member_id'], self.app_member_id)
        self.assertEqual(result['data']['consult_send'], 1)
        self.assertTrue(result['data']['is_interested'])

        # Verify the filter was called correctly
        mock_filter.assert_called_once_with(
            app_project_id=self.app_project_id,
            user_consult_id=self.user_consult_id
        )

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    def test_get_project_interest_status_not_found(self, mock_filter):
        """
        Test when project consult record is not found
        """
        mock_filter.return_value.first.return_value = None

        # Call the service
        result = ProjectInterestService.get_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertFalse(result['success'])
        self.assertEqual(result['error_code'], 3002)

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    @patch('search.services.timezone.now')
    def test_update_project_interest_status_toggle_to_interested(self, mock_now, mock_filter):
        """
        Test updating project interest status from not interested (0) to interested (1)
        """
        # Mock current time
        mock_current_time = timezone.now()
        mock_now.return_value = mock_current_time

        # Mock project consult object
        mock_project_consult = MagicMock()
        mock_project_consult.id = 1
        mock_project_consult.app_project_id = Decimal(self.app_project_id)
        mock_project_consult.user_consult_id = Decimal(self.user_consult_id)
        mock_project_consult.app_member_id = Decimal(self.app_member_id)
        mock_project_consult.consult_send = 0  # Not interested initially

        mock_filter.return_value.first.return_value = mock_project_consult
        mock_filter.return_value.update.return_value = 1  # One row updated

        # Call the service
        result = ProjectInterestService.update_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['old_consult_send'], 0)
        self.assertEqual(result['data']['new_consult_send'], 1)
        self.assertTrue(result['data']['is_interested'])
        self.assertEqual(result['data']['action'], 'interested')
        self.assertTrue(result['data']['updated'])

        # Verify update was called with correct parameters
        mock_filter.return_value.update.assert_called_once_with(
            consult_send=1,
            consult_send_date=mock_current_time,
            update_date=mock_current_time
        )

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    @patch('search.services.timezone.now')
    def test_update_project_interest_status_toggle_to_not_interested(self, mock_now, mock_filter):
        """
        Test updating project interest status from interested (1) to not interested (0)
        """
        # Mock current time
        mock_current_time = timezone.now()
        mock_now.return_value = mock_current_time

        # Mock project consult object
        mock_project_consult = MagicMock()
        mock_project_consult.id = 1
        mock_project_consult.app_project_id = Decimal(self.app_project_id)
        mock_project_consult.user_consult_id = Decimal(self.user_consult_id)
        mock_project_consult.app_member_id = Decimal(self.app_member_id)
        mock_project_consult.consult_send = 1  # Interested initially

        mock_filter.return_value.first.return_value = mock_project_consult
        mock_filter.return_value.update.return_value = 1  # One row updated

        # Call the service
        result = ProjectInterestService.update_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertTrue(result['success'])
        self.assertEqual(result['data']['old_consult_send'], 1)
        self.assertEqual(result['data']['new_consult_send'], 0)
        self.assertFalse(result['data']['is_interested'])
        self.assertEqual(result['data']['action'], 'not_interested')
        self.assertTrue(result['data']['updated'])

        # Verify update was called with correct parameters
        mock_filter.return_value.update.assert_called_once_with(
            consult_send=0,
            consult_send_date=None,
            update_date=mock_current_time
        )

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    def test_update_project_interest_status_not_found(self, mock_filter):
        """
        Test updating project interest status when record is not found
        """
        mock_filter.return_value.first.return_value = None

        # Call the service
        result = ProjectInterestService.update_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertFalse(result['success'])
        self.assertEqual(result['error_code'], 3002)

    @patch('search.services.TcdAppProjectConsult.objects.filter')
    def test_update_project_interest_status_update_failed(self, mock_filter):
        """
        Test when database update fails (returns 0 rows updated)
        """
        # Mock project consult object
        mock_project_consult = MagicMock()
        mock_project_consult.id = 1
        mock_project_consult.consult_send = 0

        mock_filter.return_value.first.return_value = mock_project_consult
        mock_filter.return_value.update.return_value = 0  # No rows updated

        # Call the service
        result = ProjectInterestService.update_project_interest_status(
            self.app_project_id, self.user_consult_id
        )

        # Assertions
        self.assertFalse(result['success'])
        self.assertEqual(result['error_code'], 3002)
