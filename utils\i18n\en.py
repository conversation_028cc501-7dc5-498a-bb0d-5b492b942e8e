# -*- coding: utf-8 -*-
"""
Error messages in English language
"""

ERROR_MESSAGES = {
    # Authentication errors (1000-1099)
    1000: "User not found",
    1001: "Invalid username or password",
    1002: "You have entered an incorrect password more than 3 times. Please wait 30 seconds to login again or click Forgot Password.",
    1003: "User account is not verified",
    1004: "Username is locked",
    1005: "Invalid token",
    1006: "Access denied",
    1010: "Account has been deleted",
    1011: "Current password is incorrect",

    # Validation errors (2000-2099)
    2000: "Invalid data provided",
    2001: "Required field missing",
    2002: "Invalid email format",
    2003: "Invalid phone number format",
    2004: "Invalid data format",
    2005: "File size exceeds limit",
    2006: "File type not supported",
    2007: "Duplicate data",
    2008: "Value out of range",
    2009: "Invalid parameter",
    2010: "PID has already been used",
    2011: "Email has already been used",
    2012: "Phone number has already been used",
    2013: "Username has already been used",
    2014: "Username is not valid",
    2015: "Username must be at least 4 characters",
    2016: "Password and confirm password do not match",
    2020: "OTP has already been verified",
    2021: "OTP has expired",
    2022: "OTP has been used",
    2023: "Invalid OTP code",
    2024: "OTP token expired",
    2025: "OTP token invalid",
    2026: "OTP token not verified",
    2027: "Failed to send email. Please try again.",
    2028: "OTP verification failed",
    2029: "Registration email must match the email used for OTP verification",

    # Database errors (3000-3099)
    3000: "Database error occurred",
    3001: "Cannot connect to database",
    3002: "Data not found",
    3005: "Cannot save data",

    # API errors (4000-4099)
    4000: "Cannot connect to ThaID",
    4001: "Invalid API key",
    4003: "API rate limit exceeded",

    # System errors (5000-5099)
    5000: "System error occurred",

}

# Payment status mapping
PAYMENT_STATUS = {
    '0': 'Pending payment',
    '1': 'Pending verification',
    '2': 'Confirmed payment',
    '3': 'Not approved'
}

PAYMENT_DESCRIPTION = {
    1: 'Consultant registration fee',
    2: 'Consultant renewal fee'
}

# Status work mapping
STATUS_WORK_MAPPING = {
    "0": 'Submit',
    "1": 'Officer review',
    "2": 'Review successful',
    "3": 'Editing opened',
    "4": 'Pending payment',
    "5": 'Pending authorization',
    "6": 'Pending authorization',
    "7": 'Submission successful',
    "8": 'Action returned',
}

STATUS_WORK_LOG_MAPPING = {
    "0": 'Submit',
    "1": 'Officer review',
    "2": 'Review successful',
    "3": 'Editing opened',
    "4": 'Pending payment',
    "5": 'Pending authorization',
    "6": 'Authorized',
    "7": 'Submission successful',
    "8": 'Action returned',
    "9": 'Edit successful',
}
