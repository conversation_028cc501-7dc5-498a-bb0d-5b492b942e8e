from django.urls import path, include
from . import views
from rest_framework.routers import DefaultRouter

app_name = 'search'

# Create router for Sector
sector_router = DefaultRouter()
sector_router.register("", views.TcdSectorViewSet, basename="sector")

# Create router for Skill
skill_router = DefaultRouter()
skill_router.register("", views.TcdSkillViewSet, basename="skill")

# Create router for Service
service_router = DefaultRouter()
service_router.register("", views.TcdServiceViewSet, basename="service")

urlpatterns = [
    # Master Data routes
    path("sector/", include(sector_router.urls)),
    path("skill/", include(skill_router.urls)),
    path("service/", include(service_router.urls)),
]
