from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from .response import get_language_from_request


class CustomPagination(PageNumberPagination):
    """
    Custom pagination class that returns data in the required format
    Supports both query parameters and headers for pagination
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 1000000

    def get_page_number(self, request, paginator):
        """
        Override to support both query params and headers
        """
        # First try query parameters (standard way)
        page_number = request.GET.get(self.page_query_param, 1)

        # If not found in query params, try headers
        if page_number == 1:  # Default value means not found in query params
            page_number = request.META.get('HTTP_PAGE', 1)

        return page_number

    def get_page_size(self, request):
        """
        Override to support both query params and headers
        """
        # First try query parameters (standard way)
        if self.page_size_query_param:
            page_size = request.GET.get(self.page_size_query_param)
            if page_size:
                try:
                    page_size = int(page_size)
                    if page_size > 0:
                        return min(page_size, self.max_page_size)
                except (ValueError, TypeError):
                    pass

        # If not found in query params, try headers
        page_size = request.META.get('HTTP_PAGE_SIZE') or request.META.get('HTTP_PER_PAGE')
        if page_size:
            try:
                page_size = int(page_size)
                if page_size > 0:
                    return min(page_size, self.max_page_size)
            except (ValueError, TypeError):
                pass

        return self.page_size

    def get_paginated_response(self, data):
        """
        Return a paginated style `Response` object for the given output data.
        """
        # Get language from request if available, otherwise default to 'th'
        language = 'th'
        if hasattr(self, 'request') and self.request:
            language = get_language_from_request(self.request)
        elif hasattr(self, '_request') and self._request:
            language = get_language_from_request(self._request)

        response_data = {
            'success': True,
            'error_code': None,
            'error_message': None,
            'data': data,
            'page': self.page.number,
            'per_page': self.page.paginator.per_page,
            'total': self.page.paginator.count,
            'has_next': self.page.has_next(),
            'api_version': 'v.0.0.1'
        }

        return Response(response_data)
