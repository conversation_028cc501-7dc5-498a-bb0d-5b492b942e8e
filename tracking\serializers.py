from rest_framework import serializers
from .models import TcdWorklist, TcdRequestType, TcdSelectType
from utils.response import get_language_from_request


class WorklistSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdWorklist model with dynamic service type name and formatted date
    """
    service_type_name = serializers.SerializerMethodField()
    status_name = serializers.SerializerMethodField()
    status_work_name = serializers.SerializerMethodField()
    formatted_send_date = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdWorklist
        fields = '__all__'
    
    def get_service_type_name(self, obj):
        """
        Get service type name based on request_type_id and select_type_id
        """
        request = self.context.get('request')
        language = get_language_from_request(request) if request else 'th'
        
        try:
            if obj.request_type_id == 1:
                # Use request_type table
                request_type = TcdRequestType.objects.filter(id=obj.request_type_id).first()
                if request_type:
                    if language == 'en' and request_type.name_en:
                        return request_type.name_en
                    return request_type.name
                return "Unknown Service Type"
            else:
                # Use select_type table
                if obj.select_type_id:
                    select_type = TcdSelectType.objects.filter(id=obj.select_type_id).first()
                    if select_type:
                        if language == 'en' and select_type.name_en:
                            return select_type.name_en
                        return select_type.name_th
                return "Unknown Service Type"
        except Exception:
            return "Unknown Service Type"
    
    def get_status_name(self, obj):
        """
        Get status name from status_work field
        """
        if obj.status_work:
            return obj.status_work
        return ""
    
    def get_status_work_name(self, obj):
        """
        Get status work name based on status_work field and language
        """
        request = self.context.get('request')
        language = get_language_from_request(request) if request else 'th'
        
        # Status work mapping
        status_work_mapping = {
            0: {
                'th': 'งานใหม่ / ยื่นเรื่อง (ฝั่งที่ปรึกษา)',
                'en': 'New Work / Submit Application (Consultant Side)'
            },
            1: {
                'th': 'กำลังตรวจสอบ / เจ้าหน้าที่กำลังตรวจสอบ (ฝั่งที่ปรึกษา)',
                'en': 'Under Review / Staff is Reviewing (Consultant Side)'
            },
            2: {
                'th': 'ตรวจสอบสำเร็จ',
                'en': 'Review Successful'
            },
            3: {
                'th': 'แก้ไขข้อมูล / เปิดการแก้ไขข้อมูล (ฝั่งที่ปรึกษา)',
                'en': 'Edit Information / Open for Editing (Consultant Side)'
            },
            4: {
                'th': 'รอชำระเงิน',
                'en': 'Waiting for Payment'
            },
            5: {
                'th': 'รออนุมัติ',
                'en': 'Waiting for Approval'
            },
            6: {
                'th': 'อนุมัติ / รออนุมัติ (ฝั่งที่ปรึกษา)',
                'en': 'Approved / Waiting for Approval (Consultant Side)'
            },
            7: {
                'th': 'สำเร็จ / ยื่นเรื่องสำเร็จ (ฝั่งที่ปรึกษา)',
                'en': 'Success / Application Successful (Consultant Side)'
            },
            8: {
                'th': 'ไม่อนุมติ / คืนเรื่อง (ฝั่งที่ปรึกษา)',
                'en': 'Not Approved / Return Application (Consultant Side)'
            }
        }
        
        try:
            if obj.status_work is None:
                return ""
            
            status_mapping = status_work_mapping.get(obj.status_work, {})
            return status_mapping.get(language, status_mapping.get('th', ''))
        except Exception:
            return ""
    
    def get_formatted_send_date(self, obj):
        """
        Format send_date to 'd MMM yyyy' format
        """
        if obj.send_date:
            # Format date based on language
            request = self.context.get('request')
            language = get_language_from_request(request) if request else 'th'
            
            if language == 'en':
                return obj.send_date.strftime('%d %b %Y')
            else:
                # Thai month names
                thai_months = {
                    1: 'ม.ค.', 2: 'ก.พ.', 3: 'มี.ค.', 4: 'เม.ย.',
                    5: 'พ.ค.', 6: 'มิ.ย.', 7: 'ก.ค.', 8: 'ส.ค.',
                    9: 'ก.ย.', 10: 'ต.ค.', 11: 'พ.ย.', 12: 'ธ.ค.'
                }
                day = obj.send_date.day
                month = thai_months.get(obj.send_date.month, str(obj.send_date.month))
                year = obj.send_date.year + 543  # Convert to Buddhist Era
                return f"{day} {month} {year}"
        return ""


class WorklistRequestSerializer(serializers.Serializer):
    """
    Serializer for worklist request parameters
    """
    page = serializers.IntegerField(required=False, default=1, min_value=1)
    page_size = serializers.IntegerField(required=False, default=10, min_value=1, max_value=100)
    
    def validate_page(self, value):
        """Validate page parameter"""
        if value < 1:
            raise serializers.ValidationError("Page must be greater than 0")
        return value
    
    def validate_page_size(self, value):
        """Validate page_size parameter"""
        if value < 1 or value > 100:
            raise serializers.ValidationError("Page size must be between 1 and 100")
        return value


class WorklistResponseSerializer(serializers.Serializer):
    """
    Serializer for worklist response data
    """
    results = WorklistSerializer(many=True)
    pagination = serializers.DictField()


class WorklistDetailRequestSerializer(serializers.Serializer):
    """
    Serializer for worklist detail request parameters
    """
    worklist_id = serializers.IntegerField(required=True, min_value=1)
    
    def validate_worklist_id(self, value):
        """Validate worklist_id parameter"""
        if value < 1:
            raise serializers.ValidationError("Worklist ID must be greater than 0")
        return value


class WorklistDetailResponseSerializer(serializers.Serializer):
    """
    Serializer for worklist detail response data
    """
    id = serializers.IntegerField()
    request_type_id = serializers.IntegerField()
    select_type_id = serializers.IntegerField(allow_null=True)
    purpose_id = serializers.DecimalField(max_digits=18, decimal_places=0, allow_null=True)
    user_consult_id = serializers.IntegerField(allow_null=True)
    
    # Dates
    send_date = serializers.DateTimeField(allow_null=True)
    pdmo_date = serializers.DateTimeField(allow_null=True)
    tcd_date = serializers.DateTimeField(allow_null=True)
    approve_date = serializers.DateTimeField(allow_null=True)
    sign_date = serializers.DateTimeField(allow_null=True)
    start_date = serializers.DateTimeField(allow_null=True)
    last_date = serializers.DateTimeField(allow_null=True)
    end_date = serializers.DateTimeField(allow_null=True)
    user_approve_date = serializers.DateTimeField(allow_null=True)
    disapprove_date = serializers.DateTimeField(allow_null=True)
    
    # Document numbers
    pdmo_number = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    tcd_number = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    register_no = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    ser_number = serializers.IntegerField(allow_null=True)
    ser_year = serializers.CharField(max_length=4, allow_null=True, allow_blank=True)
    
    # Project information
    name = serializers.CharField(allow_null=True, allow_blank=True)
    project_name = serializers.CharField(allow_null=True, allow_blank=True)
    
    # Status fields
    status = serializers.CharField(max_length=1, allow_null=True, allow_blank=True)
    status_work = serializers.CharField(max_length=1, allow_null=True, allow_blank=True)
    status_work_name = serializers.CharField(allow_null=True, allow_blank=True)
    latest_status = serializers.CharField(allow_null=True, allow_blank=True)
    
    # Additional fields
    send_doc = serializers.CharField(max_length=1, allow_null=True, allow_blank=True)
    foreign = serializers.CharField(max_length=1, allow_null=True, allow_blank=True)
    rating = serializers.CharField(max_length=50, allow_null=True, allow_blank=True)
    sector = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    skill = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    exp_consult = serializers.CharField(max_length=250, allow_null=True, allow_blank=True)
    disapprove_remark = serializers.CharField(allow_null=True, allow_blank=True)
    
    # Service type information
    service_type_name = serializers.CharField(allow_null=True, allow_blank=True)
    request_type_name = serializers.CharField(allow_null=True, allow_blank=True)
    request_type_name_en = serializers.CharField(allow_null=True, allow_blank=True)
    select_type_name_th = serializers.CharField(allow_null=True, allow_blank=True)
    select_type_name_en = serializers.CharField(allow_null=True, allow_blank=True)


class WorklistLogRequestSerializer(serializers.Serializer):
    """
    Serializer for worklist log request parameters
    """
    worklist_id = serializers.IntegerField(required=True, min_value=1)
    page = serializers.IntegerField(required=False, default=1, min_value=1)
    page_size = serializers.IntegerField(required=False, default=10, min_value=1, max_value=100)
    
    def validate_worklist_id(self, value):
        """Validate worklist_id parameter"""
        if value < 1:
            raise serializers.ValidationError("Worklist ID must be greater than 0")
        return value
    
    def validate_page(self, value):
        """Validate page parameter"""
        if value < 1:
            raise serializers.ValidationError("Page must be greater than 0")
        return value
    
    def validate_page_size(self, value):
        """Validate page_size parameter"""
        if value < 1 or value > 100:
            raise serializers.ValidationError("Page size must be between 1 and 100")
        return value


class WorklistLogItemSerializer(serializers.Serializer):
    """
    Serializer for individual worklist log item
    """
    id = serializers.IntegerField()
    worklist_id = serializers.IntegerField()
    status_name = serializers.CharField(allow_blank=True)  # ชื่อสถานะ
    status_work_name = serializers.CharField(allow_null=True, allow_blank=True)  # ชื่อสถานะงาน
    create_date = serializers.CharField(allow_blank=True)  # วันที่ดำเนินการ (formatted)
    expire_date = serializers.CharField(allow_null=True, allow_blank=True)  # ภายในวันที่ (formatted)
    detail = serializers.CharField(allow_null=True, allow_blank=True)  # รายละเอียด
    document_url = serializers.CharField(allow_null=True, allow_blank=True)  # ไฟล์แนบ URL
    edit_success_date = serializers.DateTimeField(allow_null=True)
    
    # Raw fields for reference
    status_work = serializers.CharField(allow_blank=True)
    create_date_raw = serializers.DateTimeField(allow_null=True)
    expire_date_raw = serializers.DateTimeField(allow_null=True)


class WorklistLogResponseSerializer(serializers.Serializer):
    """
    Serializer for worklist log response data
    """
    results = WorklistLogItemSerializer(many=True)
    pagination = serializers.DictField() 