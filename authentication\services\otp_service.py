# -*- coding: utf-8 -*-
"""
OTP Service
Implementation of JWT-Embedded OTP Flow (Stateless OTP Storage Using JWT Signature)
"""

import os
import logging
import random
import time
from datetime import datetime, timedelta
import jwt
from django.conf import settings
from django.utils import timezone

# Import service response utility functions
from utils.response import service_success_response, service_error_response

# Setup logging
logger = logging.getLogger(__name__)

class OTPService:
    """
    Service for OTP generation, validation, and management
    Uses JWT-Embedded approach for stateless OTP storage
    """
    
    # OTP configuration
    OTP_LENGTH = 6  # 6-digit OTP
    REF_CODE_LENGTH = 6  # 6-digit REF_CODE
    OTP_EXPIRY_MINUTES = 5  # OTP expires after 5 minutes
    MAX_VERIFICATION_ATTEMPTS = 3  # Maximum verification attempts
    
    # JWT configuration
    JWT_ALGORITHM = 'RS256'
    JWT_PRIVATE_KEY = settings.JWT_PRIVATE_KEY  # Use RSA private key for signing
    JWT_PUBLIC_KEY = settings.JWT_PUBLIC_KEY    # Use RSA public key for verification
    
    @classmethod
    def generate_otp(cls, identifier, purpose='register'):
        """
        Generate a new OTP and return it with a JWT token containing OTP details
        
        Args:
            identifier (str): User identifier (email, phone, etc.)
            purpose (str): Purpose of OTP (login, registration, reset_password, etc.)
            
        Returns:
            dict: {
                'otp': str,  # The generated OTP code
                'token': str, # JWT token with OTP details
                'expires_at': datetime # Expiry time
            }
        """
        logger.info(f"Generating OTP for {identifier} for purpose: {purpose}")
        
        # Generate random OTP
        otp_code = ''.join([str(random.randint(0, 9)) for _ in range(cls.OTP_LENGTH)])
        ref_code = ''.join([random.choice('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(cls.REF_CODE_LENGTH)])
        
        # Calculate expiry time
        expiry_time = timezone.now() + timedelta(minutes=cls.OTP_EXPIRY_MINUTES)
        
        # Create JWT payload with OTP details
        payload = {
            'identifier': identifier,
            'purpose': purpose,
            'otp_hash': cls._hash_otp(otp_code, ref_code, identifier),  # Store hash instead of actual OTP
            'created_at': timezone.now().timestamp(),
            'expires_at': expiry_time.timestamp(),
            'attempts': 0,  # Track verification attempts
            'status': 'pending'  # pending, verified, expired
        }
        
        # Generate JWT token
        token = jwt.encode(
            payload,
            cls.JWT_PRIVATE_KEY,
            algorithm=cls.JWT_ALGORITHM
        )
        
        logger.debug(f"OTP generated for {identifier}: {otp_code[:2]}****")
        logger.debug(f"REF_CODE generated for {identifier}: {ref_code}")
        return {
            'otp': otp_code,  # This should be sent to the user via SMS/email
            'ref_code': ref_code,  # This should be sent to the user via SMS/email
            'token': token,  # This should be stored on the client side
            'expires_at': expiry_time
        }
    
    @classmethod
    def verify_otp(cls, token, otp_code, ref_code):
        """
        Verify OTP against the provided token
        
        Args:
            token (str): JWT token containing OTP details
            otp_code (str): OTP code entered by user
            ref_code (str): Ref code entered by user
        Returns:
            dict: {
                'success': bool,
                'error_code': int or None,
                'message': str,
                'data': dict or None
            }
        """
        logger.info("Verifying OTP")
        
        try:
            # First check for token expiration without decoding the entire token
            # This will catch both token expiration (JWT level) and OTP expiration (application level)
            try:
                # Decode JWT token
                payload = jwt.decode(
                    token,
                    cls.JWT_PUBLIC_KEY,
                    algorithms=[cls.JWT_ALGORITHM]
                )
            except jwt.ExpiredSignatureError:
                logger.error("JWT token expired")
                return service_error_response(
                    error_code=2021  # Use OTP expired error code instead of token expired
                )
            except jwt.InvalidTokenError:
                logger.error("Invalid JWT token")
                return service_error_response(
                    error_code=2025  # Invalid token
                )
            
            # Extract token data
            identifier = payload.get('identifier')
            purpose = payload.get('purpose')
            otp_hash = payload.get('otp_hash')
            expires_at = payload.get('expires_at')
            attempts = payload.get('attempts', 0)
            status = payload.get('status')
            
            logger.debug(f"OTP verification for {identifier}, purpose: {purpose}, status: {status}")
            
            # Check if OTP is already verified
            if status == 'verified':
                logger.warning(f"OTP already verified for {identifier}")
                return service_error_response(
                    error_code=2020  # OTP already verified
                )
            
            # Check if OTP is expired
            current_time = timezone.now().timestamp()
            if expires_at and current_time > expires_at:
                logger.warning(f"OTP expired for {identifier}")
                return service_error_response(
                    error_code=2021  # OTP expired
                )
            
            # Check if maximum attempts exceeded
            if attempts >= cls.MAX_VERIFICATION_ATTEMPTS:
                logger.warning(f"Maximum OTP verification attempts exceeded for {identifier}")
                return service_error_response(
                    error_code=2022  # Max attempts exceeded
                )
            
            # Update attempts count
            attempts += 1
            
            # Verify OTP hash
            expected_hash = cls._hash_otp(otp_code, ref_code, identifier)
            if expected_hash != otp_hash:
                logger.warning(f"Invalid OTP for {identifier}")
                
                # Create new token with updated attempts count
                new_payload = payload.copy()
                new_payload['attempts'] = attempts
                new_token = jwt.encode(
                    new_payload,
                    cls.JWT_PRIVATE_KEY,
                    algorithm=cls.JWT_ALGORITHM
                )
                
                error_response = service_error_response(
                    error_code=2023  # Invalid OTP
                )
                error_response['data'] = {
                    'token': new_token,  # Return updated token
                    'attempts': attempts,
                    'remaining_attempts': cls.MAX_VERIFICATION_ATTEMPTS - attempts
                }
                return error_response
            
            # OTP is valid, update status to verified
            new_payload = payload.copy()
            new_payload['status'] = 'verified'
            new_payload['verified_at'] = current_time
            
            # Generate new token with verified status
            new_token = jwt.encode(
                new_payload,
                cls.JWT_PRIVATE_KEY,
                algorithm=cls.JWT_ALGORITHM
            )
            
            logger.info(f"OTP verification successful for {identifier}")
            
            return service_success_response(
                data={
                    'token': new_token,
                    'identifier': identifier,
                    'purpose': purpose,
                    'verified_at': datetime.fromtimestamp(current_time)
                }
            )
            
        except Exception as e:
            logger.error(f"OTP verification error: {str(e)}")
            # Log the details of the exception for debugging
            import traceback
            logger.error(f"Exception traceback: {traceback.format_exc()}")
            
            # Check if the error is related to token expiration
            if "expired" in str(e).lower():
                return service_error_response(
                    error_code=2021  # OTP expired
                )
                
            return service_error_response(
                error_code=3000  # General error
            )
    
    @classmethod
    def validate_verified_token(cls, token):
        """
        Validate an already verified OTP token and extract its data
        
        Args:
            token (str): JWT token containing OTP details
            
        Returns:
            dict: {
                'success': bool,
                'error_code': int or None,
                'message': str,
                'data': dict or None
            }
        """
        logger.info("Validating verified OTP token")
        
        try:
            # Decode JWT token
            payload = jwt.decode(
                token,
                cls.JWT_PUBLIC_KEY,
                algorithms=[cls.JWT_ALGORITHM]
            )
            
            # Extract token data
            identifier = payload.get('identifier')
            purpose = payload.get('purpose')
            status = payload.get('status')
            verified_at = payload.get('verified_at')
            
            # Check if token is verified
            if status != 'verified':
                logger.warning(f"Token not verified for {identifier}")
                return service_error_response(
                    error_code=2026  # Token not verified
                )
            
            # Return verification data
            logger.info(f"Token validation successful for {identifier}")
            return service_success_response(
                data={
                    'identifier': identifier,
                    'purpose': purpose,
                    'verified_at': datetime.fromtimestamp(verified_at) if verified_at else None
                }
            )
            
        except jwt.ExpiredSignatureError:
            logger.error("JWT token expired")
            return service_error_response(
                error_code=2024  # Token expired
            )
        except jwt.InvalidTokenError:
            logger.error("Invalid JWT token")
            return service_error_response(
                error_code=2025  # Invalid token
            )
        except Exception as e:
            logger.error(f"Token validation error: {str(e)}")
            return service_error_response(
                error_code=3000  # General error
            )
    
    @classmethod
    def _hash_otp(cls, otp_code, ref_code, identifier):
        """
        Create a hash of the OTP combined with the identifier
        
        Args:
            otp_code (str): The OTP code
            identifier (str): User identifier
            
        Returns:
            str: Hash of OTP + identifier
        """
        import hashlib
        # Combine OTP and identifier to prevent timing attacks
        combined = f"{otp_code}:{ref_code}:{identifier}"
        return hashlib.sha256(combined.encode()).hexdigest() 