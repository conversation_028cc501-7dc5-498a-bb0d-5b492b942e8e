from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .services import SurveysService
from .serializers import (
    SatisfactionSurveyMenuDisplaySerializer,
    SurveysResponseSerializer,
    QuestionnaireQuestionsResponseSerializer
)
from drf_spectacular.utils import extend_schema
from utils.response import get_language_from_request


@extend_schema(
    tags=["Surveys"],
)
@api_view(['GET'])
@permission_classes([AllowAny])
def check_satisfaction_survey_menu_display(request):
    """
    Check if satisfaction survey menu should be displayed
    
    Query Parameters:
    - question_position_id (int, optional): Question position ID to check (default: 5)
    
    Returns:
    - success (bool): Whether the operation was successful
    - data (dict): Contains display status and related information
    """
    try:
        # Get query parameters
        question_position_id = request.GET.get('question_position_id', 5)
        language = get_language_from_request(request)
        # Validate the parameter
        try:
            question_position_id = int(question_position_id)
        except ValueError:
            return Response({
                "success": False,
                "error_code": "INVALID_PARAMETER",
                "error_message": "question_position_id must be a valid integer",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Call the service
        result = SurveysService.check_satisfaction_survey_menu_display(question_position_id, language)
        
        # Return response
        if result["success"]:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        return Response({
            "success": False,
            "error_code": "UNEXPECTED_ERROR",
            "error_message": str(e),
            "data": {},
            "api_version": "v.0.0.1"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Surveys"],
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_active_questionnaires(request):
    """
    Get all active questionnaires
    
    Returns:
    - success (bool): Whether the operation was successful
    - data (dict): Contains list of active questionnaires
    """
    try:
        # Call the service
        result = SurveysService.get_active_questionnaires()
        
        # Return response
        if result["success"]:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        return Response({
            "success": False,
            "error_code": "UNEXPECTED_ERROR",
            "error_message": str(e),
            "data": {},
            "api_version": "v.0.0.1"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Surveys"],
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_questionnaire_questions(request):
    """
    Get questions for a specific questionnaire
    
    Query Parameters:
    - questionnaire_id (int, required): Questionnaire ID to get questions for
    
    Returns:
    - success (bool): Whether the operation was successful
    - data (dict): Contains questionnaire questions
    """
    try:
        # Get query parameters
        questionnaire_id = request.GET.get('questionnaire_id')
        language = get_language_from_request(request)
        
        # Validate the parameter
        if not questionnaire_id:
            return Response({
                "success": False,
                "error_code": "MISSING_PARAMETER",
                "error_message": "questionnaire_id is required",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            questionnaire_id = int(questionnaire_id)
        except ValueError:
            return Response({
                "success": False,
                "error_code": "INVALID_PARAMETER",
                "error_message": "questionnaire_id must be a valid integer",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Call the service
        result = SurveysService.get_questionnaire_questions(questionnaire_id, language)
        
        # Return response
        if result["success"]:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        return Response({
            "success": False,
            "error_code": "UNEXPECTED_ERROR",
            "error_message": str(e),
            "data": {},
            "api_version": "v.0.0.1"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Surveys"],
)
@api_view(['POST'])
@permission_classes([AllowAny])
def save_questionnaire_answers(request):
    """
    Save questionnaire answers with transaction support
    
    Authentication: Optional (JWT token)
    User Types: All users allowed
    - Authenticated users: Member ('M') and Consultant ('C')
    - Unauthenticated users: Guest ('G')
    
    Request Body:
    - questionnaire_id (int, required): Questionnaire ID
    - answers (list, required): List of answer objects with structure:
        {
            "questionnaire_question_id": int,
            "answer": int (1-5)
        }
    - additional_feedback (str, optional): Additional feedback text
    
    Returns:
    - success (bool): Whether the operation was successful
    - data (dict): Contains save operation results
    """
    try:
        # Check if user is authenticated
        if not request.user or not request.user.is_authenticated:
            # Set member_type to 'G' for unauthenticated users
            member_type = 'G'
        else:
            # Get user type from JWT token
            user_type = getattr(request.user, 'user_type', None)
            
            if not user_type:
                return Response({
                    "success": False,
                    "error_code": "INVALID_USER_TYPE",
                    "error_message": "User type not found in token",
                    "data": {},
                    "api_version": "v.0.0.1"
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Map user_type from token to member_type for service
            # Token user_type: 'member' -> member_type: 'M'
            # Token user_type: 'consultant' -> member_type: 'C'
            # For other types, default to 'G'
            if user_type == 'member':
                member_type = 'M'
            elif user_type == 'consultant':
                member_type = 'C'
            else:
                member_type = 'G'
        
        # Get data from request body
        questionnaire_id = request.data.get('questionnaire_id')
        answers = request.data.get('answers', [])
        additional_feedback = request.data.get('additional_feedback')
        
        # Validate required parameters
        if not questionnaire_id:
            return Response({
                "success": False,
                "error_code": "MISSING_PARAMETER",
                "error_message": "questionnaire_id is required",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not answers or not isinstance(answers, list):
            return Response({
                "success": False,
                "error_code": "MISSING_PARAMETER",
                "error_message": "answers must be a non-empty list",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate questionnaire_id is integer
        try:
            questionnaire_id = int(questionnaire_id)
        except ValueError:
            return Response({
                "success": False,
                "error_code": "INVALID_PARAMETER",
                "error_message": "questionnaire_id must be a valid integer",
                "data": {},
                "api_version": "v.0.0.1"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Call the service
        result = SurveysService.save_questionnaire_answers(
            questionnaire_id=questionnaire_id,
            answers=answers,
            additional_feedback=additional_feedback,
            member_type=member_type
        )
        
        # Return response
        if result["success"]:
            return Response(result, status=status.HTTP_201_CREATED)
        else:
            if result["error_code"] == "VALIDATION_ERROR":
                return Response(result, status=status.HTTP_400_BAD_REQUEST)
            else:
                return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        return Response({
            "success": False,
            "error_code": "UNEXPECTED_ERROR",
            "error_message": str(e),
            "data": {},
            "api_version": "v.0.0.1"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

