from rest_framework import serializers
from documents.models import TcdDocument

class DocumentSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    type = serializers.IntegerField()
    uuid = serializers.CharField()
    corporate_general_data_id = serializers.IntegerField(allow_null=True)
    file_size = serializers.IntegerField(allow_null=True)
    name = serializers.CharField()
    no_profit_general_data_id = serializers.IntegerField(allow_null=True)
    personal_general_data_id = serializers.IntegerField(allow_null=True)
    record_date = serializers.DateTimeField()
    status = serializers.CharField(allow_null=True)
    file_url = serializers.CharField(allow_null=True)


class DocumentRequestSerializer(serializers.Serializer):
    general_data_id = serializers.IntegerField()
    consult_type = serializers.IntegerField()
    corporate_type_id = serializers.IntegerField(required=False, allow_null=True)


class DocumentResponseSerializer(serializers.Serializer):
    success = serializers.BooleanField()
    error_code = serializers.CharField(allow_null=True)
    error_message = serializers.CharField(allow_null=True)
    data = DocumentSerializer(many=True)
    page = serializers.IntegerField()
    per_page = serializers.IntegerField()
    total = serializers.IntegerField()
    has_next = serializers.BooleanField()
    api_version = serializers.CharField()
    