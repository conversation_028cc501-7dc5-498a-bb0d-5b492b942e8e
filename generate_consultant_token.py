#!/usr/bin/env python
"""
Script สำหรับสร้าง JWT Token ของ Consultant (TcdUserConsult) พร้อมกำหนดอายุได้

Usage:
    python generate_consultant_token.py --user_id <user_id> --username <username> [--access_days <days>] [--refresh_days <days>]
    python generate_consultant_token.py --username <username> [--access_days <days>] [--refresh_days <days>]
    python generate_consultant_token.py --list-consultants
    python generate_consultant_token.py --help

Examples:
    # สร้าง token สำหรับ consultant user ID 1 และ username "consultant1" (อายุ default: access=1วัน, refresh=30วัน)
    python generate_consultant_token.py --user_id 1 --username consultant1

    # สร้าง token สำหรับ consultant โดยใช้ username เท่านั้น พร้อมกำหนดอายุ
    python generate_consultant_token.py --username consultant1 --access_days 7 --refresh_days 60

    # แสดงรายชื่อ consultant ทั้งหมด
    python generate_consultant_token.py --list-consultants
"""

import os
import sys
import django
import argparse
from datetime import datetime, timedelta
import logging

# Load environment variables from .env file
def load_env_file():
    """Load environment variables from .env file"""
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    # Handle export statements
                    if line.startswith('export '):
                        line = line[7:]  # Remove 'export '

                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value

# Load environment variables first
load_env_file()

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MCDC.settings')
django.setup()

from authentication.models import TcdUserConsult, TcdUserConsultTeam
from authentication.services import ConsultantAuthService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CustomConsultantRefreshToken:
    """
    Custom RefreshToken class สำหรับ Consultant ที่สามารถกำหนดอายุได้
    """

    @staticmethod
    def create_token(access_days=1, refresh_days=30):
        """
        สร้าง custom refresh token ที่กำหนดอายุได้

        Args:
            access_days (int): จำนวนวันของ access token
            refresh_days (int): จำนวนวันของ refresh token

        Returns:
            RefreshToken: Custom refresh token
        """
        from rest_framework_simplejwt.tokens import RefreshToken
        from datetime import timezone

        # สร้าง refresh token
        refresh = RefreshToken()

        # กำหนดเวลาหมดอายุ
        now = datetime.now(timezone.utc)
        access_exp = now + timedelta(days=access_days)
        refresh_exp = now + timedelta(days=refresh_days)

        # แปลงเป็น timestamp
        access_exp_timestamp = int(access_exp.timestamp())
        refresh_exp_timestamp = int(refresh_exp.timestamp())

        # กำหนดเวลาหมดอายุใน payload
        refresh.payload['exp'] = refresh_exp_timestamp

        # สร้าง access token ด้วยอายุที่กำหนด
        access_token = refresh.access_token
        access_token.payload['exp'] = access_exp_timestamp

        return refresh, access_token


class ConsultantTokenGenerator:
    """
    Class สำหรับสร้าง JWT Token ของ Consultant
    """

    @staticmethod
    def _create_simple_tokens(consultant, access_days=1, refresh_days=30):
        """
        สร้าง JWT tokens แบบง่าย โดยไม่บันทึกลง blacklist database

        Args:
            consultant: TcdUserConsult object
            access_days (int): จำนวนวันของ access token
            refresh_days (int): จำนวนวันของ refresh token

        Returns:
            dict: JWT tokens
        """
        try:
            # สร้าง custom token ที่กำหนดอายุได้
            refresh, access_token = CustomConsultantRefreshToken.create_token(access_days, refresh_days)

            # Get consultant team info for first_name and last_name
            consultant_team = None
            try:
                consultant_team = TcdUserConsultTeam.objects.filter(
                    user_consult=consultant,
                    is_admin=True
                ).first()
            except Exception:
                pass

            # เพิ่มข้อมูล custom ใน token
            refresh['user_id'] = int(consultant.id) if consultant.id else None
            refresh['username'] = str(consultant.username) if consultant.username else None
            refresh['user_type'] = 'consultant'
            refresh['email'] = str(consultant.email) if consultant.email else None

            # ใช้ข้อมูลจาก consultant_team ถ้ามี
            if consultant_team:
                refresh['first_name'] = str(consultant_team.first_name) if consultant_team.first_name else None
                refresh['last_name'] = str(consultant_team.last_name) if consultant_team.last_name else None
            else:
                refresh['first_name'] = None
                refresh['last_name'] = None

            # คัดลอกข้อมูลไปยัง access token ด้วย
            access_token['user_id'] = refresh['user_id']
            access_token['username'] = refresh['username']
            access_token['user_type'] = refresh['user_type']
            access_token['email'] = refresh['email']
            access_token['first_name'] = refresh['first_name']
            access_token['last_name'] = refresh['last_name']

            return {
                'refresh': str(refresh),
                'access': str(access_token),
            }
        except Exception as e:
            logger.error(f"Error creating simple tokens: {str(e)}")
            return None
    
    @staticmethod
    def list_consultants():
        """
        แสดงรายชื่อ Consultants ทั้งหมด
        """
        try:
            consultants = TcdUserConsult.objects.all().order_by('id')
            
            if not consultants.exists():
                print("ไม่พบ Consultants ในระบบ")
                return
            
            print("\n=== รายชื่อ Consultants ===")
            print(f"{'ID':<5} {'Username':<20} {'Email':<30} {'Phone':<15} {'Verify':<8}")
            print("-" * 78)
            
            for consultant in consultants:
                verify_status = "ยืนยันแล้ว" if consultant.verify == '1' else "ยังไม่ยืนยัน"
                print(f"{consultant.id:<5} {consultant.username:<20} {consultant.email:<30} {consultant.phone:<15} {verify_status:<8}")
            
            print(f"\nรวมทั้งหมด: {consultants.count()} คน")
            
        except Exception as e:
            logger.error(f"Error listing consultants: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการดึงข้อมูล Consultants: {str(e)}")
    
    @staticmethod
    def generate_consultant_token(user_id, username, access_days=1, refresh_days=30):
        """
        สร้าง JWT Token สำหรับ Consultant

        Args:
            user_id (int): ID ของ Consultant
            username (str): Username ของ Consultant
            access_days (int): จำนวนวันของ access token (default: 1)
            refresh_days (int): จำนวนวันของ refresh token (default: 30)

        Returns:
            dict: JWT tokens หรือ None ถ้าเกิดข้อผิดพลาด
        """
        try:
            # ค้นหา Consultant
            consultant = TcdUserConsult.objects.get(id=user_id, username=username)

            print(f"\n=== ข้อมูล Consultant ===")
            print(f"ID: {consultant.id}")
            print(f"Username: {consultant.username}")
            print(f"Email: {consultant.email}")
            print(f"Phone: {consultant.phone}")
            print(f"Verify Status: {'ยืนยันแล้ว' if consultant.verify == '1' else 'ยังไม่ยืนยัน'}")

            # ดึงข้อมูลจาก consultant team ถ้ามี
            try:
                consultant_team = TcdUserConsultTeam.objects.filter(
                    user_consult=consultant,
                    is_admin=True
                ).first()
                
                if consultant_team:
                    print(f"ชื่อ-นามสกุล: {consultant_team.first_name} {consultant_team.last_name}")
            except Exception:
                pass

            # สร้าง JWT Tokens โดยไม่บันทึกลง blacklist database
            try:
                # ใช้ default service ก่อน แต่ถ้า error ให้ใช้ custom token
                tokens = ConsultantAuthService.generate_tokens(consultant)
            except Exception as token_error:
                # หากเกิด error ในการบันทึก blacklist ให้สร้าง token แบบง่าย
                logger.warning(f"Blacklist database error, creating simple tokens: {str(token_error)}")
                tokens = None

            # ถ้าต้องการกำหนดอายุเอง หรือ service ไม่ทำงาน ให้ใช้ custom token
            if access_days != 1 or refresh_days != 30 or not tokens:
                logger.info(f"Creating custom tokens with access_days={access_days}, refresh_days={refresh_days}")
                tokens = ConsultantTokenGenerator._create_simple_tokens(consultant, access_days, refresh_days)

            if tokens:
                print(f"\n=== JWT Tokens (อายุ Access: {access_days} วัน, Refresh: {refresh_days} วัน) ===")
                print(f"Access Token:")
                print(f"{tokens['access']}")
                print(f"\nRefresh Token:")
                print(f"{tokens['refresh']}")

                # แสดงข้อมูลเพิ่มเติมเกี่ยวกับ token
                try:
                    from rest_framework_simplejwt.tokens import RefreshToken, AccessToken

                    refresh_token = RefreshToken(tokens['refresh'])
                    access_token = AccessToken(tokens['access'])

                    print(f"\n=== Token Information ===")
                    print(f"Token Type: consultant")
                    print(f"User ID: {refresh_token.payload.get('user_id')}")
                    print(f"Username: {refresh_token.payload.get('username')}")
                    print(f"User Type: {refresh_token.payload.get('user_type')}")
                    print(f"Email: {refresh_token.payload.get('email')}")
                    print(f"First Name: {refresh_token.payload.get('first_name')}")
                    print(f"Last Name: {refresh_token.payload.get('last_name')}")

                    # แสดงวันหมดอายุ
                    access_exp = access_token.payload.get('exp')
                    refresh_exp = refresh_token.payload.get('exp')

                    if access_exp:
                        from datetime import timezone as dt_timezone
                        access_expire_date = datetime.fromtimestamp(access_exp, tz=dt_timezone.utc)
                        print(f"Access Token หมดอายุ: {access_expire_date.strftime('%Y-%m-%d %H:%M:%S UTC')} ({access_days} วัน)")

                    if refresh_exp:
                        from datetime import timezone as dt_timezone
                        refresh_expire_date = datetime.fromtimestamp(refresh_exp, tz=dt_timezone.utc)
                        print(f"Refresh Token หมดอายุ: {refresh_expire_date.strftime('%Y-%m-%d %H:%M:%S UTC')} ({refresh_days} วัน)")

                except Exception as parse_error:
                    logger.warning(f"Could not parse token details: {str(parse_error)}")

                print(f"\n=== การใช้งาน ===")
                print(f"ใส่ Access Token ใน Header:")
                print(f"Authorization: Bearer {tokens['access']}")
                print(f"\n✅ Token สร้างสำเร็จ!")

                return tokens
            else:
                print("เกิดข้อผิดพลาดในการสร้าง Token")
                return None

        except TcdUserConsult.DoesNotExist:
            logger.error(f"Consultant not found: id={user_id}, username={username}")
            print(f"ไม่พบ Consultant ที่มี ID: {user_id} และ Username: {username}")
            return None
        except Exception as e:
            logger.error(f"Error generating consultant token: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการสร้าง Token: {str(e)}")
            return None
    
    @staticmethod
    def generate_token_by_username_only(username, access_days=1, refresh_days=30):
        """
        สร้าง JWT Token สำหรับ Consultant โดยใช้ username เท่านั้น

        Args:
            username (str): Username ของ Consultant
            access_days (int): จำนวนวันของ access token (default: 1)
            refresh_days (int): จำนวนวันของ refresh token (default: 30)

        Returns:
            dict: JWT tokens หรือ None ถ้าเกิดข้อผิดพลาด
        """
        try:
            # ค้นหา Consultant โดยใช้ username เท่านั้น
            consultant = TcdUserConsult.objects.get(username=username)

            return ConsultantTokenGenerator.generate_consultant_token(consultant.id, consultant.username, access_days, refresh_days)

        except TcdUserConsult.DoesNotExist:
            logger.error(f"Consultant not found with username: {username}")
            print(f"ไม่พบ Consultant ที่มี Username: {username}")
            return None
        except Exception as e:
            logger.error(f"Error generating consultant token: {str(e)}")
            print(f"เกิดข้อผิดพลาดในการสร้าง Token: {str(e)}")
            return None


def main():
    """
    Main function สำหรับรัน script
    """
    parser = argparse.ArgumentParser(
        description='สร้าง JWT Token สำหรับ Consultant (TcdUserConsult) พร้อมกำหนดอายุได้',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --user_id 1 --username consultant1
  %(prog)s --username consultant1 --access_days 7 --refresh_days 60
  %(prog)s --list-consultants
        """
    )

    parser.add_argument('--user_id', type=int, help='ID ของ Consultant')
    parser.add_argument('--username', type=str, help='Username ของ Consultant')
    parser.add_argument('--access_days', type=int, default=1, help='จำนวนวันของ Access Token (default: 1)')
    parser.add_argument('--refresh_days', type=int, default=30, help='จำนวนวันของ Refresh Token (default: 30)')
    parser.add_argument('--list-consultants', action='store_true', help='แสดงรายชื่อ Consultants ทั้งหมด')
    
    args = parser.parse_args()
    
    # ตรวจสอบ arguments
    if args.list_consultants:
        ConsultantTokenGenerator.list_consultants()
    elif args.username:
        # ตรวจสอบค่า access_days และ refresh_days
        if args.access_days < 1:
            print("Error: access_days ต้องมากกว่า 0")
            sys.exit(1)
        if args.refresh_days < 1:
            print("Error: refresh_days ต้องมากกว่า 0")
            sys.exit(1)

        if args.user_id:
            # ใช้ทั้ง user_id และ username
            ConsultantTokenGenerator.generate_consultant_token(args.user_id, args.username, args.access_days, args.refresh_days)
        else:
            # ใช้ username เท่านั้น
            ConsultantTokenGenerator.generate_token_by_username_only(args.username, args.access_days, args.refresh_days)
    else:
        parser.print_help()
        print("\nกรุณาระบุ --username หรือ --list-consultants")
        sys.exit(1)


if __name__ == '__main__':
    main()
