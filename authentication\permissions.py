from rest_framework.permissions import BasePermission


class IsMember(BasePermission):
    """
    Permission class สำหรับตรวจสอบว่าผู้ใช้เป็น Member หรือไม่
    """
    
    def has_permission(self, request, view):
        """
        Return True if permission is granted, False otherwise.
        """
        return (
            request.user and 
            hasattr(request.user, 'user_type') and 
            request.user.user_type == 'member'
        )


class IsConsultant(BasePermission):
    """
    Permission class สำหรับตรวจสอบว่าผู้ใช้เป็น Consultant หรือไม่
    """
    
    def has_permission(self, request, view):
        """
        Return True if permission is granted, False otherwise.
        """
        return (
            request.user and 
            hasattr(request.user, 'user_type') and 
            request.user.user_type == 'consultant'
        )


class IsMemberOrConsultant(BasePermission):
    """
    Permission class สำหรับตรวจสอบว่าผู้ใช้เป็น Member หรือ Consultant
    """
    
    def has_permission(self, request, view):
        """
        Return True if permission is granted, False otherwise.
        """
        return (
            request.user and 
            hasattr(request.user, 'user_type') and 
            request.user.user_type in ['member', 'consultant']
        )


class IsActiveMember(BasePermission):
    """
    Permission class สำหรับตรวจสอบว่าผู้ใช้เป็น Member ที่ active หรือไม่
    """
    
    def has_permission(self, request, view):
        """
        Return True if permission is granted, False otherwise.
        """
        if not (request.user and hasattr(request.user, 'user_type') and request.user.user_type == 'member'):
            return False
        
        # ตรวจสอบสถานะ member
        return request.user.status == '1'


class IsConsultantWithType(BasePermission):
    """
    Permission class สำหรับตรวจสอบ Consultant ตาม consult_type
    """
    
    def __init__(self, allowed_types=None):
        self.allowed_types = allowed_types or []
    
    def has_permission(self, request, view):
        """
        Return True if permission is granted, False otherwise.
        """
        if not (request.user and hasattr(request.user, 'user_type') and request.user.user_type == 'consultant'):
            return False
        
        if not self.allowed_types:
            return True
        
        return request.user.consult_type in self.allowed_types 