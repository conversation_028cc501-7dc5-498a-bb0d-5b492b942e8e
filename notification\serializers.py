from rest_framework import serializers
from .models import TcdAppNotification, TcdNotiConsult


class TcdAppNotificationSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdAppNotification model
    """
    
    class Meta:
        model = TcdAppNotification
        fields = [
            'id',
            'noti_type',
            'type',
            'main_id',
            'ref_id',
            'header',
            'detail',
            'create_date',
            'is_read'
        ]
        read_only_fields = ['id', 'create_date']


class TcdNotiConsultSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdNotiConsult model
    """
    
    class Meta:
        model = TcdNotiConsult
        fields = [
            'id',
            'type',
            'detail',
            'date',
            'read',
            'user_consult_id'
        ]
        read_only_fields = ['id', 'date'] 