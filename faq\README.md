# FAQ API Documentation

## Overview

This document provides comprehensive API specification for the FAQ System, including REST API endpoints for FAQ categories and FAQ items. The system provides read-only access to FAQ data for mobile applications with multi-language support (Thai/English).

## Features

- **No Authentication Required**: Public access for all FAQ endpoints
- **Multi-language Support**: Thai and English content with automatic language selection
- **Mobile-Optimized**: Designed for mobile application consumption
- **Pagination**: CustomPagination with query string parameters
- **Filtering**: Category-based and platform-specific filtering
- **Search**: Full-text search across questions and answers
- **Performance Optimized**: Efficient database queries with proper relationships

## API Endpoints

### Base URL
```
/api/faq/
```

### 1. FAQ Categories

#### List FAQ Categories
**Endpoint:** `GET /api/faq/category/`

**Description:** Get all FAQ categories with pagination

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 10, max: 100)
- `search`: Search in category names (Thai and English)

**Response Format:**
```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": [
        {
            "id": 1,
            "name_th": "หมวดหมู่ทั่วไป",
            "name_en": "General Category",
            "order": 1,
            "display_text": "หมวดหมู่ทั่วไป : General Category"
        }
    ],
    "page": 1,
    "per_page": 10,
    "total": 1,
    "has_next": false
}
```

#### Get FAQ Category Details
**Endpoint:** `GET /api/faq/category/{id}/`

**Description:** Get specific FAQ category by ID

**Response Format:**
```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": {
        "category": {
            "id": 1,
            "name_th": "หมวดหมู่ทั่วไป",
            "name_en": "General Category",
            "order": 1,
            "display_text": "หมวดหมู่ทั่วไป : General Category"
        }
    }
}
```

### 2. FAQ Items

#### List FAQs
**Endpoint:** `GET /api/faq/faq/`

**Description:** Get all FAQs with pagination and filtering

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 10, max: 100)
- `category`: Filter by category ID
- `platform`: Filter by platform ('app', 'web', or 'both')
- `search`: Search in questions and answers (Thai and English)
- `status`: Filter by status (default: active only)

**Response Format:**
```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": [
        {
            "id": 1,
            "question": "คำถามตัวอย่าง",
            "category_name": "หมวดหมู่ทั่วไป",
            "is_app": true,
            "is_web": true
        }
    ],
    "page": 1,
    "per_page": 10,
    "total": 1,
    "has_next": false
}
```

#### Get FAQ Details
**Endpoint:** `GET /api/faq/faq/{id}/`

**Description:** Get specific FAQ by ID with complete information

**Response Format:**
```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": {
        "faq": {
            "id": 1,
            "question_th": "คำถามตัวอย่าง",
            "question_en": "Sample Question",
            "answer_th": "คำตอบตัวอย่าง",
            "answer_en": "Sample Answer",
            "category": {
                "id": 1,
                "name_th": "หมวดหมู่ทั่วไป",
                "name_en": "General Category",
                "order": 1,
                "display_text": "หมวดหมู่ทั่วไป : General Category"
            },
            "category_name": "หมวดหมู่ทั่วไป",
            "question": "คำถามตัวอย่าง",
            "answer": "คำตอบตัวอย่าง",
            "is_app": true,
            "is_web": true,
            "create_date": "2024-01-01T00:00:00Z",
            "update_date": "2024-01-01T00:00:00Z",
            "status": true
        }
    }
}
```

#### Get FAQs by Category
**Endpoint:** `GET /api/faq/faq/by_category/{category_id}/`

**Description:** Get FAQs filtered by specific category

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 10, max: 100)
- `platform`: Filter by platform ('app', 'web', or 'both')

**Response Format:**
```json
{
    "success": true,
    "error_code": null,
    "error_message": null,
    "data": {
        "category_id": 1,
        "faqs": [
            {
                "id": 1,
                "question": "คำถามตัวอย่าง",
                "category_name": "หมวดหมู่ทั่วไป",
                "is_app": true,
                "is_web": true
            }
        ]
    },
    "page": 1,
    "per_page": 10,
    "total": 1,
    "has_next": false
}
```

## Language Support

The API automatically selects content based on user language preference:

1. **User Language Detection**: 
   - From authenticated user's `lang` field
   - Defaults to Thai ('th') if not specified

2. **Content Selection**:
   - If user language is 'en' and English content exists, returns English
   - Otherwise returns Thai content
   - Falls back to available content if preferred language is missing

## Error Handling

All endpoints use consistent error response format:

```json
{
    "success": false,
    "error_code": 3002,
    "error_message": "ไม่พบข้อมูล",
    "data": {}
}
```

Common error codes:
- `2000`: Invalid data provided (validation error)
- `3002`: Data not found
- `5000`: System error

## cURL Examples

### Get FAQ Categories
```bash
curl -X GET "http://localhost:8000/api/faq/category/?page=1&page_size=10"
```

### Search FAQ Categories
```bash
curl -X GET "http://localhost:8000/api/faq/category/?search=general"
```

### Get FAQ Category Details
```bash
curl -X GET "http://localhost:8000/api/faq/category/1/"
```

### Get All FAQs
```bash
curl -X GET "http://localhost:8000/api/faq/faq/?page=1&page_size=10"
```

### Get FAQs by Category
```bash
curl -X GET "http://localhost:8000/api/faq/faq/?category=1"
```

### Get FAQs by Platform
```bash
curl -X GET "http://localhost:8000/api/faq/faq/?platform=app"
```

### Search FAQs
```bash
curl -X GET "http://localhost:8000/api/faq/faq/?search=keyword"
```

### Get FAQ Details
```bash
curl -X GET "http://localhost:8000/api/faq/faq/1/"
```

### Get FAQs by Category (Custom Endpoint)
```bash
curl -X GET "http://localhost:8000/api/faq/faq/by_category/1/?page=1&page_size=10"
```

## Implementation Details

### Models Used
- `TcdAppFaqcategory`: FAQ categories with Thai/English names and ordering
- `TcdAppFaq`: FAQ items with questions/answers in both languages

### Serializers
- `TcdAppFaqcategorySerializer`: Full category serialization with display text
- `TcdAppFaqSerializer`: Complete FAQ serialization with category information
- `TcdAppFaqListSerializer`: Optimized serializer for list views

### ViewSets
- `TcdAppFaqcategoryViewSet`: ReadOnlyModelViewSet for FAQ categories
- `TcdAppFaqViewSet`: ReadOnlyModelViewSet for FAQs with custom filtering

### Key Features
- **Performance Optimization**: Uses `select_related()` for efficient database queries
- **Custom Pagination**: Implements CustomPagination for mobile-optimized responses
- **Error Handling**: Comprehensive error handling with APIResponse methods
- **Filtering**: Multiple filtering options (category, platform, status)
- **Search**: Full-text search across multiple fields
- **Language Support**: Dynamic content selection based on user preference
