# -*- coding: utf-8 -*-
"""
Chat URL patterns
"""
from django.urls import path
from . import views

app_name = 'chat'

urlpatterns = [
    # Mobile Chat API endpoints
    path('start/', views.start_chat, name='start_chat'),
    path('rooms/', views.mobile_chat_rooms, name='mobile_chat_rooms'),
    path('history/', views.chat_history, name='chat_history'),
    path('send/', views.send_message, name='send_message'),
    path('mark-read/', views.mark_as_read, name='mark_as_read'),
    path('mark-all-read/', views.mark_all_as_read, name='mark_all_as_read'),
    path('online-status/', views.update_online_status, name='update_online_status'),

    # File handling endpoints
    path('files/', views.list_chat_files, name='list_chat_files'),
    path('file/download/<int:message_id>/', views.download_chat_file, name='download_chat_file'),
    path('file/metadata/<int:message_id>/', views.get_file_metadata, name='get_file_metadata'),

    # Chat utilities
    path('prompts/', views.chat_prompts, name='chat_prompts'),
    path('prompts-answer/<int:prompt_id>/', views.answer_prompt, name='answer_prompt'),
    path('settings/', views.chat_settings, name='chat_settings'),
    path('check-status/', views.get_chat_check_status, name='get_chat_check_status'),
]
