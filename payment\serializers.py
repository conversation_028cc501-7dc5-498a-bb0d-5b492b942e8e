from rest_framework import serializers
from .models import TcdPayment


class PaymentDetailListRequestSerializer(serializers.Serializer):
    """
    Serializer for payment detail list request parameters
    """
    page = serializers.IntegerField(required=False, min_value=1, help_text="หน้าที่ต้องการ (default: 1)")
    page_size = serializers.IntegerField(required=False, min_value=1, max_value=100, help_text="จำนวนรายการต่อหน้า (default: 10, max: 100)")


class PaymentDetailItemSerializer(serializers.Serializer):
    """
    Serializer for individual payment detail item
    """
    bill_no = serializers.CharField(help_text="เลขที่ใบแจ้งการชำระ")
    cgd_ref1 = serializers.CharField(help_text="รหัสอ้างอิง (Ref.) 1")
    create_date = serializers.CharField(help_text="วันที่เรียกเก็บ (d MMM yyyy HH:mm)")
    expire_date = serializers.CharField(help_text="วันที่ครบกำหนด (d MMM yyyy HH:mm)")
    status_text = serializers.Char<PERSON>ield(help_text="สถานะ")
    description = serializers.Char<PERSON>ield(help_text="รายละเอียด")
    pay_date = serializers.CharField(help_text="วันที่ดำเนินการ")
    amount_formatted = serializers.CharField(help_text="ยอดชำระทั้งหมด")
    show_payment_proof_button = serializers.BooleanField(help_text="แสดงปุ่มหลักฐานการชำระ")
    show_payment_button = serializers.BooleanField(help_text="แสดงปุ่มชำระค่าธรรมเนียม")


class PaymentDetailListResponseSerializer(serializers.Serializer):
    """
    Serializer for payment detail list response
    """
    payments = PaymentDetailItemSerializer(many=True, help_text="รายการข้อมูลการชำระ")
    page = serializers.IntegerField(help_text="หน้าปัจจุบัน")
    per_page = serializers.IntegerField(help_text="จำนวนรายการต่อหน้า")
    total = serializers.IntegerField(help_text="จำนวนรายการทั้งหมด")
    has_next = serializers.BooleanField(help_text="มีหน้าถัดไปหรือไม่")


class PaymentDetailByIdSerializer(serializers.Serializer):
    """
    Serializer for individual payment detail by ID
    """
    id = serializers.IntegerField(help_text="Payment ID")
    request_type = serializers.IntegerField(help_text="ประเภทคำขอ")
    number = serializers.CharField(help_text="หมายเลข")
    ref2 = serializers.CharField(help_text="รหัสอ้างอิง 2")
    biller_id = serializers.CharField(help_text="Biller ID")
    bill_no = serializers.CharField(help_text="เลขที่ใบแจ้งการชำระ")
    cgd_ref1 = serializers.CharField(help_text="รหัสอ้างอิง (Ref.) 1")
    cgd_ref2 = serializers.CharField(help_text="รหัสอ้างอิง (Ref.) 2")
    barcode_string = serializers.CharField(help_text="Barcode String")
    qrcode_string = serializers.CharField(help_text="QR Code String")
    response_pmt1 = serializers.CharField(help_text="Response PMT 1")
    response_pmt2 = serializers.CharField(help_text="Response PMT 2")
    src_qrcode = serializers.CharField(help_text="Source QR Code")
    src_payin = serializers.CharField(help_text="Source Pay In")
    amount = serializers.CharField(help_text="จำนวนเงิน")
    pay_amount = serializers.CharField(help_text="จำนวนเงินที่ชำระ")
    worklist_id = serializers.IntegerField(help_text="Worklist ID")
    user_consult_id = serializers.IntegerField(help_text="User Consult ID")
    src = serializers.CharField(help_text="Source")
    create_date = serializers.CharField(help_text="วันที่เรียกเก็บ (d MMM yyyy HH:mm)")
    expire_date = serializers.CharField(help_text="วันที่ครบกำหนด (d MMM yyyy HH:mm)")
    status = serializers.CharField(help_text="สถานะ")
    pay_date = serializers.CharField(help_text="วันที่ดำเนินการ")
    amount_formatted = serializers.CharField(help_text="ยอดชำระทั้งหมด (รูปแบบเงิน)")
    show_payment_proof_button = serializers.BooleanField(help_text="แสดงปุ่มหลักฐานการชำระ")
    show_payment_button = serializers.BooleanField(help_text="แสดงปุ่มชำระค่าธรรมเนียม")
    status_text = serializers.CharField(help_text="ข้อความสถานะ")
    description = serializers.CharField(help_text="รายละเอียด")


class PaymentDetailByIdResponseSerializer(serializers.Serializer):
    """
    Serializer for payment detail by ID response
    """
    success = serializers.BooleanField(help_text="สถานะการดำเนินการ")
    error_code = serializers.CharField(allow_null=True, help_text="รหัสข้อผิดพลาด")
    error_message = serializers.CharField(allow_null=True, help_text="ข้อความข้อผิดพลาด")
    data = PaymentDetailByIdSerializer(help_text="ข้อมูลการชำระ")
    api_version = serializers.CharField(help_text="เวอร์ชัน API") 