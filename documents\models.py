from django.db import models


class TcdDocument(models.Model):
    type = models.IntegerField()
    uuid = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    corporate_general_data_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    file_size = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=200, db_collation='Thai_CI_AI')
    no_profit_general_data_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    personal_general_data_id = models.DecimalField(max_digits=19, decimal_places=0, blank=True, null=True)
    record_date = models.DateTimeField()
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_document'

