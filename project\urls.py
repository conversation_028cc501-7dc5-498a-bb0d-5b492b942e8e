from django.urls import path
from . import views

app_name = 'project'

urlpatterns = [
    # Project Count API
    path("count/", views.project_count, name="project_count"),

    # Project Search API
    path("search/", views.project_search, name="project_search"),

    # Member Project Search API
    path("member/search/", views.member_project_search, name="member_project_search"),

    # Project Detail API
    path("<str:project_id>/", views.project_detail, name="project_detail"),
    
    # Project Detail Extended API
    path("detail/extended/", views.get_project_detail_extended, name="get_project_detail_extended"),

    # Project Suitable Consultants API
    path("<str:project_id>/consultants/", views.get_suitable_consultants, name="get_suitable_consultants"),

    # Project Interested Consultants API
    path("<str:project_id>/interested/", views.get_project_interested_consultants, name="get_project_interested_consultants"),

    # Project Favorite Consultants API
    path("<str:project_id>/favorites/", views.get_project_favorite_consultants, name="get_project_favorite_consultants"),

    # Project View Count Management APIs
    path("<str:project_id>/view/increment/", views.increment_project_view, name="increment_project_view"),

    # Project Matching View Count Management APIs
    path("<str:app_project_id>/matching/<str:user_consult_id>/view/increment/",
         views.increment_project_matching_view,
         name="increment_project_matching_view"),

    # Consultant Member View Count Management APIs
    path("consultant/member-view/increment/",
         views.increment_consultant_member_view,
         name="increment_consultant_member_view"),

    # Project Interest Management APIs
    path("interest/status/",
         views.get_project_interest_status,
         name="get_project_interest_status"),
    path("interest/update/",
         views.update_project_interest_status,
         name="update_project_interest_status"),

    # Member Favorite Management APIs
    path("<str:app_project_id>/favorite/<str:user_consult_id>/",
         views.get_project_member_favorite_status,
         name="get_project_member_favorite_status"),
    path("favorite/update/",
         views.update_project_member_favorite_status,
         name="update_project_member_favorite_status"),
    
    # Get Corporate Type API
    path("corporate/type/", views.get_corporate_type, name="get_corporate_type"),
    
    # Consultant Experience API
    path("consultant/experience/", 
         views.get_consultant_experience, 
         name="get_consultant_experience"),
    
    # Consultant Suitable Projects API
    path("consultant/suitable/", 
         views.get_consultant_suitable_projects, 
         name="get_consultant_suitable_projects"),
    
    # Consultant Favorite Projects API
    path("consultant/favorite/", 
         views.get_consultant_favorite_projects, 
         name="get_consultant_favorite_projects"),
    
    # Consultant Favorite Status Management APIs
    path("<str:app_project_id>/consultant-favorite/",
         views.get_consultant_favorite_status,
         name="get_consultant_favorite_status"),
    path("<str:app_project_id>/consultant-favorite/update/",
         views.update_consultant_favorite_status,
         name="update_consultant_favorite_status"),
] 