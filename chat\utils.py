import os
import uuid
import logging
import base64
import mimetypes
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile
from io import BytesIO

logger = logging.getLogger(__name__)


def validate_file_type_and_size(file, file_type):
    """
    Validate file type and size based on requirements
    
    Args:
        file: Django UploadedFile object
        file_type: 'image' or 'document'
    
    Returns:
        dict: {'valid': bool, 'error': str or None}
    """
    # File type constraints
    CONSTRAINTS = {
        'image': {
            'allowed_types': ['image/jpeg', 'image/jpg', 'image/png'],
            'allowed_extensions': ['.jpg', '.jpeg', '.png'],
            'max_size': 5 * 1024 * 1024,  # 5MB
        },
        'document': {
            'allowed_types': [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'allowed_extensions': ['.pdf', '.doc', '.docx', '.xls', '.xlsx'],
            'max_size': 5 * 1024 * 1024,  # 5MB
        }
    }
    
    if file_type not in CONSTRAINTS:
        return {'valid': False, 'error': 'Invalid file type specified'}
    
    constraints = CONSTRAINTS[file_type]
    
    # Check file size
    if file.size > constraints['max_size']:
        max_size_mb = constraints['max_size'] / (1024 * 1024)
        return {'valid': False, 'error': f'File too large. Maximum size: {max_size_mb}MB'}
    
    # Check file extension
    file_extension = os.path.splitext(file.name)[1].lower()
    if file_extension not in constraints['allowed_extensions']:
        return {'valid': False, 'error': f'Invalid file extension. Allowed: {", ".join(constraints["allowed_extensions"])}'}
    
    # Check MIME type
    if hasattr(file, 'content_type') and file.content_type:
        if file.content_type not in constraints['allowed_types']:
            return {'valid': False, 'error': f'Invalid file type. Allowed: {", ".join(constraints["allowed_extensions"])}'}
    
    return {'valid': True, 'error': None}


def save_uploaded_file(file, file_type):
    """
    Save uploaded file to UPLOAD_DIR with proper naming
    
    Args:
        file: Django UploadedFile object
        file_type: 'image' or 'document'
    
    Returns:
        dict: {'success': bool, 'file_path': str, 'error': str or None}
    """
    try:
        # Validate file first
        validation = validate_file_type_and_size(file, file_type)
        if not validation['valid']:
            return {'success': False, 'file_path': None, 'error': validation['error']}
        
        # Get upload directory from settings
        upload_dir = getattr(settings, 'UPLOAD_DIR', 'uploads')
        
        # Create subdirectory based on file type
        subdirectory = getattr(settings, 'CHAT_SUB_DIR', '')
        
        # Generate unique filename to avoid conflicts
        file_extension = os.path.splitext(file.name)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"
        
        # Full path within upload directory
        full_path = os.path.join(upload_dir, subdirectory, unique_filename)
        
        # Ensure directory exists
        directory = os.path.dirname(full_path)
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
        
        # Save file
        with open(full_path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
        
        logger.info(f"File saved successfully: {full_path}")
        return {
            'success': True,
            'file_path': unique_filename,  # Return only filename for database storage
            'error': None
        }
        
    except Exception as e:
        logger.error(f"Error saving file: {str(e)}")
        return {'success': False, 'file_path': None, 'error': f'Failed to save file: {str(e)}'}


def determine_message_type(file):
    """
    Determine message type based on file content type
    
    Args:
        file: Django UploadedFile object
    
    Returns:
        str: 'I' for images, 'D' for documents
    """
    if hasattr(file, 'content_type') and file.content_type:
        if file.content_type.startswith('image/'):
            return 'I'
        else:
            return 'D'
    
    # Fallback to file extension
    file_extension = os.path.splitext(file.name)[1].lower()
    image_extensions = ['.jpg', '.jpeg', '.png']
    
    if file_extension in image_extensions:
        return 'I'
    else:
        return 'D'


def save_websocket_file(file_data):
    """
    Save file data from WebSocket message to UPLOAD_DIR

    Args:
        file_data: Dict containing file information from WebSocket
                  Expected keys: 'name', 'data' (base64), 'type' (MIME type)

    Returns:
        dict: {'success': bool, 'file_path': str, 'file_name': str, 'message_type': str, 'error': str or None}
    """
    try:
        logger.info(f"save_websocket_file called with file_data: {file_data}")

        if not file_data or not isinstance(file_data, dict):
            error_msg = f'Invalid file data: {type(file_data)} - {file_data}'
            logger.error(error_msg)
            return {'success': False, 'file_path': None, 'file_name': None, 'message_type': None, 'error': error_msg}

        file_name = file_data.get('name', '')
        file_base64 = file_data.get('data', '')
        file_mime_type = file_data.get('type', '')

        logger.info(f"Extracted from file_data - name: '{file_name}', data length: {len(file_base64) if file_base64 else 0}, type: '{file_mime_type}'")

        if not file_name or not file_base64:
            error_msg = f'Missing file name or data - name: "{file_name}", data_length: {len(file_base64) if file_base64 else 0}'
            logger.error(error_msg)
            return {'success': False, 'file_path': None, 'file_name': None, 'message_type': None, 'error': error_msg}

        # Decode base64 data
        try:
            # Remove data URL prefix if present (e.g., "data:image/png;base64,")
            if ',' in file_base64:
                file_base64 = file_base64.split(',')[1]

            file_content = base64.b64decode(file_base64)
        except Exception as e:
            return {'success': False, 'file_path': None, 'file_name': None, 'message_type': None, 'error': f'Invalid base64 data: {str(e)}'}

        # Determine file type and message type
        file_extension = os.path.splitext(file_name)[1].lower()

        # Guess MIME type if not provided
        if not file_mime_type:
            file_mime_type, _ = mimetypes.guess_type(file_name)
            file_mime_type = file_mime_type or 'application/octet-stream'

        # Determine if it's an image or document
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        document_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.rtf']

        if file_extension in image_extensions or file_mime_type.startswith('image/'):
            file_type = 'image'
            message_type = 'I'
        elif file_extension in document_extensions:
            file_type = 'document'
            message_type = 'D'
        else:
            file_type = 'document'  # Default to document for unknown types
            message_type = 'D'

        # Create a temporary file-like object for validation
        file_obj = InMemoryUploadedFile(
            file=BytesIO(file_content),
            field_name='file',
            name=file_name,
            content_type=file_mime_type,
            size=len(file_content),
            charset=None
        )

        # Validate file
        validation = validate_file_type_and_size(file_obj, file_type)
        if not validation['valid']:
            return {'success': False, 'file_path': None, 'file_name': None, 'message_type': None, 'error': validation['error']}

        # Get upload directory from settings
        upload_dir = getattr(settings, 'UPLOAD_DIR', 'uploads')

        # Create subdirectory based on file type
        subdirectory = getattr(settings, 'CHAT_SUB_DIR', '')
        logger.info(f"subdirectory: {subdirectory}")

        # Generate unique filename to avoid conflicts
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"

        # Full path within upload directory
        full_path = os.path.join(upload_dir, subdirectory, unique_filename)

        # Ensure directory exists
        directory = os.path.dirname(full_path)
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        # Save file
        with open(full_path, 'wb') as destination:
            destination.write(file_content)

        logger.info(f"WebSocket file saved successfully: {full_path}")
        return {
            'success': True,
            'file_path': unique_filename,  # Return only filename for database storage
            'file_name': file_name,      # Original filename
            'message_type': message_type, # 'I' for image, 'D' for document
            'error': None
        }

    except Exception as e:
        logger.error(f"Error saving WebSocket file: {str(e)}")
        return {'success': False, 'file_path': None, 'file_name': None, 'message_type': None, 'error': f'Failed to save file: {str(e)}'}


def get_file_url(file_path):
    """
    Generate full URL for uploaded file

    Args:
        file_path: Relative path to file

    Returns:
        str: Full URL to file
    """
    base_url = getattr(settings, 'BASE_URL', 'http://localhost:8080')
    media_prefix = getattr(settings, 'MEDIA_PREFIX', '/media/')

    return f"{base_url}{media_prefix}{file_path}"


def get_full_url_for_message(file_path, subdirectory):
    """
    Generate full URL for uploaded file from message
    """
    base_url = getattr(settings, 'BASE_FILE_URL')
    media_prefix = getattr(settings, 'MEDIA_PREFIX', '')
    return f"{base_url}{media_prefix}{subdirectory}{file_path}"


def get_websocket_url(endpoint_path):
    """
    Generate WebSocket URL using configurable settings

    Args:
        endpoint_path: WebSocket endpoint path (e.g., '/ws/chat/room/consultation_123/')
        token: Optional JWT token to include as query parameter

    Returns:
        str: Complete WebSocket URL
    """
    websocket_base_url = getattr(settings, 'WEBSOCKET_BASE_URL', 'ws://localhost:8080')

    # Ensure endpoint_path starts with /
    if not endpoint_path.startswith('/'):
        endpoint_path = '/' + endpoint_path

    # Build base URL
    websocket_url = f"{websocket_base_url}{endpoint_path}"
    
    return websocket_url


def get_websocket_room_url(room_id):
    """
    Generate WebSocket URL for chat room

    Args:
        room_id: Chat room ID
        token: Optional JWT token to include as query parameter

    Returns:
        str: Complete WebSocket URL for chat room
    """
    endpoint_path = f"/ws/chat/room/{room_id}/"
    return get_websocket_url(endpoint_path)


def get_websocket_session_url(session_id, token=None):
    """
    Generate WebSocket URL for chat session

    Args:
        session_id: Chat session ID
        token: Optional JWT token to include as query parameter

    Returns:
        str: Complete WebSocket URL for chat session
    """
    endpoint_path = f"/ws/chat/session/{session_id}/"
    return get_websocket_url(endpoint_path, token)
