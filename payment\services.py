from django.db.models import Q
from django.core.paginator import Paginator
from datetime import datetime
import logging
from django.conf import settings

from .models import TcdPayment
from utils.response import service_success_response, service_error_response
from utils.i18n import th, en

logger = logging.getLogger(__name__)


class PaymentDetailService:
    """
    Service class for handling payment detail operations
    """
    
    @staticmethod
    def get_status_text(status, language='th'):
        """
        Get status text based on status code and language
        
        Args:
            status (str): Status code ('0', '1', '2', '3')
            language (str): Language code ('th' or 'en')
            
        Returns:
            str: Status text in specified language
        """
        return th.PAYMENT_STATUS.get(status, '') if language == 'th' else en.PAYMENT_STATUS.get(status, '')
    
    @staticmethod
    def get_description_text(request_type, language='th'):
        """
        Get description text based on request_type and language
        
        Args:
            request_type (int): Request type (1: registration, 2: renewal)
            language (str): Language code ('th' or 'en')
            
        Returns:
            str: Description text in specified language
        """
        return th.PAYMENT_DESCRIPTION.get(request_type, '') if language == 'th' else en.PAYMENT_DESCRIPTION.get(request_type, '')
    
    @staticmethod
    def format_datetime(dt, language='th'):
        """
        Format datetime to required format (d MMM yyyy HH:mm)
        
        Args:
            dt (datetime): Datetime object
            language (str): Language code ('th' or 'en')
            
        Returns:
            str: Formatted datetime string
        """
        if not dt:
            return "-"
        
        try:
            if language == 'th':
                # Thai month names
                months_th = [
                    'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
                    'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
                ]
                month_name = months_th[dt.month - 1]
                year = dt.year + 543  # Convert to Buddhist year
                return f"{dt.day} {month_name} {year} {dt.strftime('%H:%M')}"
            else:
                # English format
                return dt.strftime('%d %b %Y %H:%M')
        except Exception as e:
            logger.error(f"Error formatting datetime: {str(e)}")
            return "-"
    
    @staticmethod
    def format_amount(amount):
        """
        Format amount to money format with 2 decimal places
        
        Args:
            amount (str): Amount string
            
        Returns:
            str: Formatted amount string
        """
        try:
            if not amount:
                return "0.00"
            
            # Convert to float and format with 2 decimal places and thousand separators
            amount_float = float(amount)
            return f"{amount_float:,.2f}"
        except (ValueError, TypeError):
            return "0.00"
    
    @staticmethod
    def get_payment_detail_list(user_consult_id, page=1, page_size=10, language='th'):
        """
        Get payment detail list for a consultant
        
        Args:
            user_consult_id (int): Consultant user ID
            page (int): Page number (default: 1)
            page_size (int): Items per page (default: 10)
            language (str): Language code ('th' or 'en')
            
        Returns:
            dict: Service response with payment details
        """
        try:
            # Query payments for the consultant, ordered by create_date desc
            queryset = TcdPayment.objects.filter(
                user_consult_id=user_consult_id
            ).order_by('-create_date')
            
            # Limit to 10 latest records as specified
            queryset = queryset[:10]
            
            # Apply pagination
            paginator = Paginator(queryset, page_size)
            
            # Validate page number
            if page < 1:
                page = 1
            if page > paginator.num_pages and paginator.num_pages > 0:
                page = paginator.num_pages
                
            page_obj = paginator.get_page(page)
            
            # Format payment data
            payments = []
            for payment in page_obj:
                # Get description based on request_type
                # Note: We'll use payment.request_type since worklist table is not available
                description = PaymentDetailService.get_description_text(
                    payment.request_type, language
                )
                
                # Get status text
                status_text = PaymentDetailService.get_status_text(
                    payment.status, language
                )
                
                # Format amount
                amount_formatted = PaymentDetailService.format_amount(payment.amount)
                
                # Determine button visibility based on status
                show_payment_proof_button = payment.status in ['0', '1']  # Status 0 or 1
                show_payment_button = payment.status == '0'  # Only status 0
                
                base_file_url = getattr(settings, 'BASE_FILE_URL', '')
                payment_sub_dir = getattr(settings, 'PAYMENT_SUB_DIR', '')
                payment_file_url = f"{base_file_url}{payment_sub_dir}"
                
                payment_item = {
                    'id': payment.id,
                    'request_type': payment.request_type,
                    'number': payment.number or '',
                    'ref2': payment.ref2 or '',
                    'biller_id': payment.biller_id or '',
                    'bill_no': payment.bill_no or '',
                    'cgd_ref1': payment.cgd_ref1 or '',
                    'cgd_ref2': payment.cgd_ref2 or '',
                    'barcode_string': payment.barcode_string or '',
                    'qrcode_string': payment.qrcode_string or '',
                    'response_pmt1': payment.response_pmt1 or '',
                    'response_pmt2': payment.response_pmt2 or '',
                    'src_qrcode': f"{payment_file_url}{payment.src_qrcode}" if payment.src_qrcode else '',
                    'src_payin': f"{payment_file_url}{payment.src_payin}" if payment.src_payin else '',
                    'amount': payment.amount or '',
                    'pay_amount': payment.pay_amount or '',
                    'worklist_id': payment.worklist_id or '',
                    'user_consult_id': payment.user_consult_id or '',
                    'src': payment.src or '',
                    'create_date': payment.create_date or '',
                    'expire_date': payment.expire_date or '',
                    'status': payment.status or '',
                    'pay_date': payment.pay_date or '',
                    'amount_formatted': amount_formatted,
                    'show_payment_proof_button': show_payment_proof_button,
                    'show_payment_button': show_payment_button,
                    'status_text': status_text,
                    'description': description
                }
                payments.append(payment_item)
            
            # Prepare response data
            response_data = {
                'payments': payments,
                'page': page_obj.number,
                'per_page': page_size,
                'total': paginator.count,
                'has_next': page_obj.has_next()
            }
            
            return service_success_response(data=response_data, language=language)
            
        except Exception as e:
            logger.error(f"Error in get_payment_detail_list: {str(e)}")
            return service_error_response(error_code=5000, language=language)

    @staticmethod
    def get_payment_detail_by_id(payment_id, user_consult_id, language='th'):
        """
        Get payment detail by ID for a consultant
        
        Args:
            payment_id (int): Payment ID
            user_consult_id (int): Consultant user ID
            language (str): Language code ('th' or 'en')
            
        Returns:
            dict: Service response with payment detail
        """
        try:
            # Query payment by ID and user_consult_id for security
            payment = TcdPayment.objects.filter(
                id=payment_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not payment:
                return service_error_response(error_code=5000, language=language)  # Not found
            
            # Get description based on request_type
            description = PaymentDetailService.get_description_text(
                payment.request_type, language
            )
            
            # Get status text
            status_text = PaymentDetailService.get_status_text(
                payment.status, language
            )
            
            # Format amount
            amount_formatted = PaymentDetailService.format_amount(payment.amount)
            
            # Determine button visibility based on status
            show_payment_proof_button = payment.status in ['0', '1']  # Status 0 or 1
            show_payment_button = payment.status == '0'  # Only status 0
            
            base_file_url = getattr(settings, 'BASE_FILE_URL', '')
            payment_sub_dir = getattr(settings, 'PAYMENT_SUB_DIR', '')
            payment_file_url = f"{base_file_url}{payment_sub_dir}"
            
            payment_detail = {
                'id': payment.id,
                'request_type': payment.request_type,
                'number': payment.number or '',
                'ref2': payment.ref2 or '',
                'biller_id': payment.biller_id or '',
                'bill_no': payment.bill_no or '',
                'cgd_ref1': payment.cgd_ref1 or '',
                'cgd_ref2': payment.cgd_ref2 or '',
                'barcode_string': payment.barcode_string or '',
                'qrcode_string': payment.qrcode_string or '',
                'response_pmt1': payment.response_pmt1 or '',
                'response_pmt2': payment.response_pmt2 or '',
                'src_qrcode': f"{payment_file_url}{payment.src_qrcode}" if payment.src_qrcode else '',
                'src_payin': f"{payment_file_url}{payment.src_payin}" if payment.src_payin else '',
                'amount': payment.amount or '',
                'pay_amount': payment.pay_amount or '',
                'worklist_id': payment.worklist_id or '',
                'user_consult_id': payment.user_consult_id or '',
                'src': payment.src or '',
                'create_date': payment.create_date or '',
                'expire_date': payment.expire_date or '',
                'status': payment.status or '',
                'pay_date': payment.pay_date or '',
                'amount_formatted': amount_formatted,
                'show_payment_proof_button': show_payment_proof_button,
                'show_payment_button': show_payment_button,
                'status_text': status_text,
                'description': description
            }
            
            return service_success_response(data=payment_detail, language=language)
            
        except Exception as e:
            logger.error(f"Error in get_payment_detail_by_id: {str(e)}")
            return service_error_response(error_code=5000, language=language) 