from django.db import models


class TcdAppNotification(models.Model):
    noti_type = models.CharField(max_length=50, db_collation='Thai_CI_AI')
    type = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    main_id = models.IntegerField(blank=True, null=True)
    ref_id = models.IntegerField(blank=True, null=True)
    header = models.TextField(db_collation='Thai_CI_AI')
    detail = models.TextField(db_collation='Thai_CI_AI')
    create_date = models.DateTimeField()
    is_read = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'tcd_app_notification'


class TcdNotiConsult(models.Model):
    type = models.IntegerField(blank=True, null=True)
    detail = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    date = models.DateTimeField(blank=True, null=True)
    read = models.Char<PERSON>ield(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    user_consult_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_noti_consult'
