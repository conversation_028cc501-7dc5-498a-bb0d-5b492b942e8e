from rest_framework import serializers
from MCDC.models import TcdSetting, TcdCorporateType, TcdCalendar, TcdNews, TcdNewscategory


class TcdSettingSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdSetting model
    """
    report_logo = serializers.CharField(required=False, allow_blank=True)
    report_header = serializers.CharField(required=False, allow_blank=True)
    
    class Meta:
        model = TcdSetting
        fields = '__all__'
        
    def to_representation(self, instance):
        """
        Convert model instance to a formatted representation.
        """
        data = super().to_representation(instance)
        
        # Format payment-related fields
        payment_fields = [
            'pay_independent', 'pay_independent_renew', 'pay_independent_charge',
            'pay_juristic', 'pay_juristic_renew', 'pay_juristic_charge'
        ]
        
        for field in payment_fields:
            if data[field] is not None:
                data[f"{field}_formatted"] = f"{data[field]:,}"
                
        return data

    
class TcdCorporateTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdCorporateType model
    """
    class Meta:
        model = TcdCorporateType
        fields = '__all__'


class TcdCalendarSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdCalendar model
    """
    class Meta:
        model = TcdCalendar
        fields = '__all__'


class TcdNewscategorySerializer(serializers.ModelSerializer):
    """
    Serializer for TcdNewscategory model
    """
    class Meta:
        model = TcdNewscategory
        fields = '__all__'


class TcdNewsSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdNews model
    """
    newscategory = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdNews
        fields = '__all__'

    def get_newscategory(self, obj):
        from MCDC.models import TcdNewscategory
        try:
            category = TcdNewscategory.objects.get(id=obj.newscategory_id)
            return {
                "name": category.name,
                "order": category.order
            }
        except TcdNewscategory.DoesNotExist:
            return {
                "name": None,
                "order": None
            }

