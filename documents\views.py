
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema
from documents.serializers import DocumentResponseSerializer, DocumentRequestSerializer, DocumentSerializer
from utils.response import APIResponse
from utils.response import get_language_from_request
from utils.pagination import CustomPagination
from documents.models import TcdDocument
from rest_framework import status
from django.conf import settings
import logging
logger = logging.getLogger(__name__)

@extend_schema(
    summary="Get document list",
    description="ดึงรายการข้อมูลเอกสารของที่ปรึกษา",
    request=DocumentRequestSerializer,
    responses={
        200: DocumentResponseSerializer,
    },
    tags=["Documents"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_document_list(request):
    serializer = DocumentRequestSerializer(data=request.data)
    if serializer.is_valid():
        # Get language from request
        language = get_language_from_request(request)
        
        # Check if user is authenticated and is a consultant
        if not hasattr(request.user, 'user_type') or request.user.user_type != 'consultant':
            return APIResponse.error(
                error_code=1006,  # Access denied
                language=language,
                status_code=status.HTTP_403_FORBIDDEN
            )
                
        general_data_id = serializer.validated_data.get('general_data_id')
        consult_type = serializer.validated_data.get('consult_type')
        corporate_type_id = serializer.validated_data.get('corporate_type_id')
        
        if consult_type == 1:
            queryset = TcdDocument.objects.filter(personal_general_data_id=general_data_id, status='1')
        elif consult_type == 2:
            if corporate_type_id is not None and corporate_type_id == 1:
                queryset = TcdDocument.objects.filter(corporate_general_data_id=general_data_id, status='1')
            else:
                queryset = TcdDocument.objects.filter(no_profit_general_data_id=general_data_id, status='1')
        else:
            return APIResponse.error(
                error_code=2000,
                data={},
                language=language,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        queryset = queryset.order_by('-record_date')
        
        # Apply pagination
        paginator = CustomPagination()
        paginator.request = request
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        
        # Serialize the paginated data
        serializer = DocumentSerializer(paginated_queryset, many=True)
        
        # Add file_url to each document
        base_file_url = getattr(settings, 'BASE_FILE_URL', '')
        document_sub_dir = getattr(settings, 'DOCUMENT_SUB_DIR', '')
        document_file_url = f"{base_file_url}{document_sub_dir}"
        for document in serializer.data:
            document['file_url'] = f"{document_file_url}{document.get('uuid')}"
        
        # Return paginated response
        return paginator.get_paginated_response(serializer.data)
    
    else:
        language = get_language_from_request(request)
        return APIResponse.error(
            error_code=5000,
            data={},
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

