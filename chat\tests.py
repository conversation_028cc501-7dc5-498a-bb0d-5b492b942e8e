from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User
from unittest.mock import Mock, patch
from django.utils import timezone

from .models import TcdChat
from authentication.models import TcdUserConsult
from MCDC.models import TcdUsers


class MarkAllAsReadTestCase(APITestCase):
    """
    Test cases for the mark_all_as_read functionality
    """

    def setUp(self):
        """Set up test data"""
        # Create mock consultant user
        self.consultant_user = Mock()
        self.consultant_user.id = 1
        self.consultant_user.user_type = 'consultant'
        self.consultant_user.username = 'test_consultant'

        # Create mock staff user
        self.staff_user = Mock()
        self.staff_user.id = 2
        self.staff_user.user_type = 'staff'
        self.staff_user.username = 'test_staff'

        # Create mock member user (should be blocked)
        self.member_user = Mock()
        self.member_user.id = 3
        self.member_user.user_type = 'member'
        self.member_user.username = 'test_member'

        self.url = reverse('chat:mark_all_as_read')

    @patch('chat.views.validate_chat_access')
    @patch('chat.views.TcdChat.objects')
    @patch('chat.views.get_language_from_request')
    def test_consultant_mark_all_as_read_success(self, mock_get_lang, mock_chat_objects, mock_validate):
        """Test consultant successfully marking all messages as read"""
        # Mock validation success
        mock_validate.return_value = (True, None)
        mock_get_lang.return_value = 'th'

        # Mock queryset and update
        mock_filter = Mock()
        mock_filter.update.return_value = 5  # 5 messages updated
        mock_chat_objects.filter.return_value = mock_filter

        # Set authenticated user
        self.client.force_authenticate(user=self.consultant_user)

        # Make request
        response = self.client.post(self.url, {})

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify correct filter was called for consultant
        mock_chat_objects.filter.assert_called_with(
            user_consult_id=1,
            consult_read='0'
        )

        # Verify update was called with correct value
        mock_filter.update.assert_called_with(consult_read='1')

    @patch('chat.views.validate_chat_access')
    @patch('chat.views.TcdChat.objects')
    @patch('chat.views.TcdUserConsult.objects')
    @patch('chat.views.get_language_from_request')
    def test_staff_mark_all_as_read_success(self, mock_get_lang, mock_consult_objects, mock_chat_objects, mock_validate):
        """Test staff successfully marking all messages as read"""
        # Mock validation success
        mock_validate.return_value = (True, None)
        mock_get_lang.return_value = 'th'

        # Mock consultant exists
        mock_consult_objects.get.return_value = Mock()

        # Mock staff participation check
        mock_participation_filter = Mock()
        mock_participation_filter.exists.return_value = True

        # Mock update filter
        mock_update_filter = Mock()
        mock_update_filter.update.return_value = 3  # 3 messages updated

        # Configure mock to return different filters for different calls
        mock_chat_objects.filter.side_effect = [mock_participation_filter, mock_update_filter]

        # Set authenticated user
        self.client.force_authenticate(user=self.staff_user)

        # Make request with consultant ID
        response = self.client.post(self.url, {'user_consult_id': 1})

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify consultant existence check
        mock_consult_objects.get.assert_called_with(id=1)

        # Verify staff participation check
        self.assertEqual(mock_chat_objects.filter.call_count, 2)

        # Verify update was called with correct value
        mock_update_filter.update.assert_called_with(users_read='1')

    @patch('chat.views.validate_chat_access')
    def test_member_access_blocked(self, mock_validate):
        """Test that members are completely blocked from accessing the endpoint"""
        # Mock validation failure for member
        mock_validate.return_value = (False, Mock(status_code=401))

        # Set authenticated user as member
        self.client.force_authenticate(user=self.member_user)

        # Make request
        response = self.client.post(self.url, {})

        # Verify member is blocked
        self.assertEqual(response.status_code, 401)

    @patch('chat.views.validate_chat_access')
    @patch('chat.views.get_language_from_request')
    def test_staff_missing_user_consult_id(self, mock_get_lang, mock_validate):
        """Test staff request without required user_consult_id parameter"""
        # Mock validation success
        mock_validate.return_value = (True, None)
        mock_get_lang.return_value = 'th'

        # Set authenticated user as staff
        self.client.force_authenticate(user=self.staff_user)

        # Make request without user_consult_id
        response = self.client.post(self.url, {})

        # Verify validation error
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch('chat.views.validate_chat_access')
    @patch('chat.views.TcdUserConsult.objects')
    @patch('chat.views.get_language_from_request')
    def test_staff_invalid_consultant_id(self, mock_get_lang, mock_consult_objects, mock_validate):
        """Test staff request with non-existent consultant ID"""
        # Mock validation success
        mock_validate.return_value = (True, None)
        mock_get_lang.return_value = 'th'

        # Mock consultant not found
        from authentication.models import TcdUserConsult
        mock_consult_objects.get.side_effect = TcdUserConsult.DoesNotExist()

        # Set authenticated user as staff
        self.client.force_authenticate(user=self.staff_user)

        # Make request with invalid consultant ID
        response = self.client.post(self.url, {'user_consult_id': 999})

        # Verify not found error
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @patch('chat.views.validate_chat_access')
    @patch('chat.views.TcdChat.objects')
    @patch('chat.views.TcdUserConsult.objects')
    @patch('chat.views.get_language_from_request')
    def test_staff_no_participation_access_denied(self, mock_get_lang, mock_consult_objects, mock_chat_objects, mock_validate):
        """Test staff access denied when they haven't participated in the conversation"""
        # Mock validation success
        mock_validate.return_value = (True, None)
        mock_get_lang.return_value = 'th'

        # Mock consultant exists
        mock_consult_objects.get.return_value = Mock()

        # Mock no staff participation
        mock_participation_filter = Mock()
        mock_participation_filter.exists.return_value = False
        mock_chat_objects.filter.return_value = mock_participation_filter

        # Set authenticated user as staff
        self.client.force_authenticate(user=self.staff_user)

        # Make request
        response = self.client.post(self.url, {'user_consult_id': 1})

        # Verify unauthorized error
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
