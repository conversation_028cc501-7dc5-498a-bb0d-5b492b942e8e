from rest_framework import serializers
from .models import TcdAppFaqcategory, TcdAppFaq


class TcdAppFaqcategorySerializer(serializers.ModelSerializer):
    """
    Serializer for FAQ Category model
    Provides both Thai and English names with display formatting
    """
    display_text = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdAppFaqcategory
        fields = ['id', 'name_th', 'name_en', 'order', 'display_text']
        read_only_fields = ['id']
    
    def get_display_text(self, obj) -> str:
        """
        Create display text in format: name_th : name_en
        """
        parts = []
        if obj.name_th:
            parts.append(obj.name_th)
        if obj.name_en:
            parts.append(obj.name_en)
        return ' : '.join(parts)


class TcdAppFaqSerializer(serializers.ModelSerializer):
    """
    Serializer for FAQ model
    Includes category information and language-specific content
    """
    category = TcdAppFaqcategorySerializer(source='app_faqcategory', read_only=True)
    
    class Meta:
        model = TcdAppFaq
        fields = '__all__'
    
