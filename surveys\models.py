from django.db import models


class TcdQuestionposition(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=255, db_collation='Thai_CI_AI')
    questionnaire_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_questionposition'


class TcdQuestion(models.Model):
    question_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    question_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    count = models.IntegerField()
    questioncategory_id = models.IntegerField()
    createdate = models.DateTimeField()
    createuser = models.IntegerField()
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_question'


class TcdQuestioncategory(models.Model):
    name = models.Cha<PERSON><PERSON><PERSON>(max_length=255, db_collation='Thai_CI_AI')
    createdate = models.DateTimeField()
    createuser = models.IntegerField()
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_questioncategory'


class TcdQuestionnaire(models.Model):
    id = models.IntegerField(primary_key=True)
    name_th = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    name_en = models.CharField(max_length=255, db_collation='Thai_CI_AI')
    count = models.IntegerField()
    createdate = models.DateTimeField()
    createuser = models.IntegerField()
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI')

    class Meta:
        managed = False
        db_table = 'tcd_questionnaire'


class TcdQuestionnaireQuestion(models.Model):
    questionnaire_id = models.IntegerField()
    question_id = models.IntegerField()
    order = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_questionnaire_question'


class TcdQuestionnaireQuestionAnswer(models.Model):
    questionnaire_question_id = models.IntegerField()
    type = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    date = models.DateField()
    answer = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'tcd_questionnaire_question_answer'


class TcdQuestionnaireQuestionOtheranswer(models.Model):
    questionnaire_id = models.IntegerField()
    type = models.CharField(max_length=1, db_collation='Thai_CI_AI')
    date = models.DateField()
    answer = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_questionnaire_question_otheranswer'

