from typing import Dict, Any, List
from django.db import transaction
from django.utils import timezone
from .models import TcdQuestionposition, TcdQuestionnaire, TcdQuestionnaireQuestion, TcdQuestion, TcdQuestionnaireQuestionAnswer, TcdQuestionnaireQuestionOtheranswer


class SurveysService:
    """
    Service class for surveys-related operations
    """
    
    @staticmethod
    def check_satisfaction_survey_menu_display(question_position_id: int = 5, language: str = 'th') -> Dict[str, Any]:
        """
        Check if satisfaction survey menu should be displayed based on question position and questionnaire status
        
        Args:
            question_position_id (int): The question position ID to check (default: 5)
            language (str): The language to use for the response (default: 'th')
        Returns:
            Dict[str, Any]: Response with display status and data
        """
        try:
            # Use ORM instead of raw SQL
            # First get the question position
            question_position = TcdQuestionposition.objects.filter(id=question_position_id).first()
            
            if not question_position or not question_position.questionnaire_id:
                # No question position found or no questionnaire associated
                response_data = {
                    "question_position_id": question_position_id,
                    "should_display": False,
                    "row_count": 0,
                    "questionnaire_id": None,
                    "questionnaire_name": None
                }
            else:
                # Check if the associated questionnaire is active
                questionnaire = TcdQuestionnaire.objects.filter(
                    id=question_position.questionnaire_id,
                    status='1'
                ).first()
                
                should_display = questionnaire is not None
                questionnaire_id = questionnaire.id if questionnaire else None
                questionnaire_name = questionnaire.name_th if language == 'th' else questionnaire.name_en if language == 'en' else None
                row_count = 1 if should_display else 0
                
                response_data = {
                    "question_position_id": question_position_id,
                    "should_display": should_display,
                    "row_count": row_count,
                    "questionnaire_id": questionnaire_id,
                    "questionnaire_name": questionnaire_name
                }
            
            return {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": response_data,
                "api_version": "v.0.0.1"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error_code": "SURVEYS_SERVICE_ERROR",
                "error_message": str(e),
                "data": {},
                "api_version": "v.0.0.1"
            }
    
    @staticmethod
    def get_active_questionnaires() -> Dict[str, Any]:
        """
        Get all active questionnaires
        
        Returns:
            Dict[str, Any]: Response with active questionnaires
        """
        try:
            # Use ORM instead of raw SQL
            questionnaires = TcdQuestionnaire.objects.filter(
                status='1'
            ).order_by('-createdate')
            
            # Convert to list of dictionaries
            results = []
            for questionnaire in questionnaires:
                results.append({
                    'id': questionnaire.id,
                    'name_th': questionnaire.name_th,
                    'name_en': questionnaire.name_en,
                    'count': questionnaire.count,
                    'createdate': questionnaire.createdate,
                    'createuser': questionnaire.createuser,
                    'status': questionnaire.status
                })
            
            return {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": {
                    "questionnaires": results,
                    "total": len(results)
                },
                "api_version": "v.0.0.1"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error_code": "SURVEYS_SERVICE_ERROR",
                "error_message": str(e),
                "data": {},
                "api_version": "v.0.0.1"
            } 

    @staticmethod
    def get_questionnaire_questions(questionnaire_id: int, language: str = 'th') -> Dict[str, Any]:
        """
        Get questions for a specific questionnaire
        
        Args:
            questionnaire_id (int): The questionnaire ID to get questions for
            language (str): The language to use for question text (default: 'th')
        Returns:
            Dict[str, Any]: Response with questionnaire questions
        """
        try:
            # Get questionnaire questions ordered by order field
            questionnaire_questions = TcdQuestionnaireQuestion.objects.filter(
                questionnaire_id=questionnaire_id
            ).order_by('order')
            
            # Convert to list of dictionaries
            results = []
            for qq in questionnaire_questions:
                # Get the question object
                question = TcdQuestion.objects.filter(id=qq.question_id).first()
                if question:
                    results.append({
                        'id': qq.id,
                        'question_th': question.question_th,
                        'question_en': question.question_en,
                        'order': qq.order
                    })
            
            return {
                "success": True,
                "error_code": None,
                "error_message": None,
                "data": {
                    "questionnaire_id": questionnaire_id,
                    "questions": results,
                    "total": len(results)
                },
                "api_version": "v.0.0.1"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error_code": "SURVEYS_SERVICE_ERROR",
                "error_message": str(e),
                "data": {},
                "api_version": "v.0.0.1"
            } 

    @staticmethod
    def save_questionnaire_answers(
        questionnaire_id: int,
        answers: List[Dict[str, Any]],
        additional_feedback: str = None,
        member_type: str = 'G'
    ) -> Dict[str, Any]:
        """
        Save questionnaire answers with transaction support and rollback on error
        
        Args:
            questionnaire_id (int): The questionnaire ID
            answers (List[Dict]): List of answer dictionaries with structure:
                {
                    'questionnaire_question_id': int,
                    'answer': int (1-5)
                }
            additional_feedback (str): Optional additional feedback text
            member_type (str): Member type - 'C' for consultant, 'M' for member, 'G' for guest
        Returns:
            Dict[str, Any]: Response with success status and data
        """
        try:
            with transaction.atomic():
                current_date = timezone.now().date()
                
                # Validate member type
                if member_type not in ['C', 'M', 'G']:
                    raise ValueError("Invalid member_type. Must be 'C', 'M', or 'G'")
                
                # Insert questionnaire answers
                for answer_data in answers:
                    questionnaire_question_id = answer_data.get('questionnaire_question_id')
                    answer_value = answer_data.get('answer')
                    
                    # Validate answer value (1-5)
                    if not isinstance(answer_value, int) or answer_value < 1 or answer_value > 5:
                        raise ValueError(f"Invalid answer value: {answer_value}. Must be between 1-5")
                    
                    # Validate questionnaire question exists
                    questionnaire_question = TcdQuestionnaireQuestion.objects.filter(
                        id=questionnaire_question_id,
                        questionnaire_id=questionnaire_id
                    ).first()
                    
                    if not questionnaire_question:
                        raise ValueError(f"Questionnaire question with ID {questionnaire_question_id} not found")
                    
                    # Create answer record
                    TcdQuestionnaireQuestionAnswer.objects.create(
                        questionnaire_question_id=questionnaire_question_id,
                        date=current_date,
                        answer=answer_value,
                        type=member_type
                    )
                
                # Insert additional feedback if provided
                if additional_feedback and additional_feedback.strip():
                    # Get questionnaire from question position
                    question_position = TcdQuestionposition.objects.filter(
                        questionnaire_id=questionnaire_id
                    ).first()
                    
                    if question_position:
                        TcdQuestionnaireQuestionOtheranswer.objects.create(
                            questionnaire_id=questionnaire_id,
                            date=current_date,
                            answer=additional_feedback.strip(),
                            type=member_type
                        )
                
                return {
                    "success": True,
                    "error_code": None,
                    "error_message": None,
                    "data": {
                        "questionnaire_id": questionnaire_id,
                        "answers_saved": len(answers),
                        "additional_feedback_saved": bool(additional_feedback and additional_feedback.strip()),
                        "member_type": member_type,
                        "date": current_date.isoformat()
                    },
                    "api_version": "v.0.0.1"
                }
                
        except ValueError as e:
            return {
                "success": False,
                "error_code": "VALIDATION_ERROR",
                "error_message": str(e),
                "data": {},
                "api_version": "v.0.0.1"
            }
        except Exception as e:
            return {
                "success": False,
                "error_code": "SURVEYS_SERVICE_ERROR",
                "error_message": str(e),
                "data": {},
                "api_version": "v.0.0.1"
            } 