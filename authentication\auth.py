"""
Custom authentication backends for the MCDC project
"""
from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import AnonymousUser
import logging

from authentication.models import TcdAppMember, TcdUserConsult

logger = logging.getLogger(__name__)


class MCDCUserModelBackend(BaseBackend):
    """
    Custom authentication backend that can handle both Member and Consultant models
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate against TcdAppMember or TcdUserConsult models
        """
        from authentication.utils import verify_password
        
        # First try Member model
        try:
            if username:
                try:
                    member = TcdAppMember.objects.get(username=username)
                    if verify_password(password, member.password):
                        # Add authentication methods
                        self._add_auth_methods(member)
                        member.user_type = 'member'
                        return member
                except TcdAppMember.DoesNotExist:
                    pass
                
                # Then try Consultant model
                try:
                    consultant = TcdUserConsult.objects.get(username=username)
                    if verify_password(password, consultant.password):
                        # Add authentication methods
                        self._add_auth_methods(consultant)
                        consultant.user_type = 'consultant'
                        return consultant
                except TcdUserConsult.DoesNotExist:
                    pass
        except Exception as e:
            logger.error(f"Error in MCDCUserModelBackend.authenticate: {str(e)}")
            
        return None
    
    def get_user(self, user_id):
        """
        Get user by ID from either TcdAppMember or TcdUserConsult models
        """
        # First try Member model
        try:
            member = TcdAppMember.objects.get(id=user_id)
            # Add authentication methods
            self._add_auth_methods(member)
            member.user_type = 'member'
            return member
        except TcdAppMember.DoesNotExist:
            pass
            
        # Then try Consultant model
        try:
            consultant = TcdUserConsult.objects.get(id=user_id)
            # Add authentication methods
            self._add_auth_methods(consultant)
            consultant.user_type = 'consultant'
            return consultant
        except TcdUserConsult.DoesNotExist:
            pass
            
        return None
    
    def _add_auth_methods(self, user):
        """
        Add Django auth methods to user model
        """
        # Define methods required by Django auth
        def get_all_permissions(obj=None):
            return set()

        def has_perm(perm, obj=None):
            return False

        def has_perms(perm_list, obj=None):
            return False

        def has_module_perms(app_label):
            return False

        # Make sure is_anonymous is False
        user.is_anonymous = False
        
        # Instead of using a property, which causes setter issues,
        # use a direct attribute for is_authenticated
        user.is_authenticated = True
        
        # Also set _is_authenticated as a direct attribute for our custom checks
        setattr(user, '_is_authenticated', True)
        
        # Add methods if they don't exist
        if not hasattr(user, 'get_all_permissions'):
            user.get_all_permissions = get_all_permissions
        if not hasattr(user, 'has_perm'):
            user.has_perm = has_perm
        if not hasattr(user, 'has_perms'):
            user.has_perms = has_perms
        if not hasattr(user, 'has_module_perms'):
            user.has_module_perms = has_module_perms
            
        return user 