# -*- coding: utf-8 -*-
"""
Chat serializers for API responses
"""
from rest_framework import serializers
from django.utils import timezone
from .models import TcdChat, TcdChatPrompt, TcdSettingChat
from authentication.models import TcdApp<PERSON><PERSON>ber, Tcd<PERSON>ser<PERSON>onsult
from MCDC.models import TcdUserConsult as UserConsult


class ChatMessageSerializer(serializers.ModelSerializer):
    """
    Serializer for chat messages with enhanced file handling
    """
    sender_name = serializers.SerializerMethodField()
    sender_type = serializers.SerializerMethodField()
    file_src = serializers.SerializerMethodField()
    has_file = serializers.SerializerMethodField()

    class Meta:
        model = TcdChat
        fields = [
            'id', 'type', 'message', 'file_name', 'file_src',
            'user_consult', 'users', 'consult_read', 'users_read',
            'date', 'sender_name', 'sender_type', 'has_file'
        ]
        read_only_fields = ['id', 'date', 'sender_name', 'sender_type', 'file_src', 'has_file']
    
    def get_sender_name(self, obj):
        """Get sender name based on user type"""
        try:
            if obj.users:
                # Staff member sent the message
                return f"{obj.users.firstname} {obj.users.lastname}".strip() or obj.users.username
            elif obj.user_consult:
                # Consultant sent the message
                return getattr(obj.user_consult, 'name', obj.user_consult.username)
            return "Unknown User"
        except Exception:
            return "Unknown User"
    
    def get_sender_type(self, obj):
        """Get sender type"""
        try:
            if obj.users:
                return "staff"
            elif obj.user_consult:
                return "consultant"
            return "unknown"
        except Exception:
            return "unknown"
    

    def get_has_file(self, obj):
        """Check if message has a file attachment"""
        return bool(obj.file_src and obj.file_name)

    def get_file_src(self, obj):
        """Get file source with /files/ prefix"""
        if not obj.file_src:
            return None

        # Add /files/ prefix to the file source path
        return f"{obj.file_src}"


class ChatRoomSerializer(serializers.Serializer):
    """
    Serializer for chat room information
    """
    room_id = serializers.CharField()
    room_name = serializers.CharField()
    room_type = serializers.ChoiceField(choices=[
        ('general', 'General Chat'),
        ('private', 'Private Chat'),
        ('consultation', 'Consultation Chat')
    ])
    participants = serializers.ListField(child=serializers.DictField())
    last_message = ChatMessageSerializer(read_only=True, allow_null=True)
    unread_count = serializers.IntegerField(default=0)
    created_at = serializers.DateTimeField(default=timezone.now)


class ChatPromptSerializer(serializers.ModelSerializer):
    """
    Serializer for chat prompts/quick replies
    """
    question = serializers.SerializerMethodField()
    answer = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdChatPrompt
        fields = [
            'id', 'question', 'answer', 'status',
            'create_date', 'update_date'
        ]
        read_only_fields = ['id', 'create_date', 'update_date']
    
    def get_question(self, obj):
        """Get question based on language preference"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            # Get language preference from user or request
            lang = getattr(request.user, 'lang', 'th') if hasattr(request.user, 'lang') else 'th'
            if lang == 'en':
                return obj.question_en
        return obj.question_th
    
    def get_answer(self, obj):
        """Get answer based on language preference"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            # Get language preference from user or request
            lang = getattr(request.user, 'lang', 'th') if hasattr(request.user, 'lang') else 'th'
            if lang == 'en':
                return obj.answer_en
        return obj.answer_th


class ChatSettingSerializer(serializers.ModelSerializer):
    """
    Serializer for chat settings
    """
    detail = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdSettingChat
        fields = [
            'id', 'start_time', 'end_time', 'detail', 'status'
        ]
    
    def get_detail(self, obj):
        """Get detail based on language preference"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            # Get language preference from user or request
            lang = getattr(request.user, 'lang', 'th') if hasattr(request.user, 'lang') else 'th'
            if lang == 'en' and obj.detail_en:
                return obj.detail_en
        return obj.detail_th


class SendMessageSerializer(serializers.Serializer):
    """
    Serializer for sending messages with file upload support
    """
    message = serializers.CharField(required=False, allow_blank=True, max_length=5000)
    file_name = serializers.CharField(required=False, allow_blank=True, max_length=500)
    file_src = serializers.CharField(required=False, allow_blank=True, max_length=100)
    message_type = serializers.ChoiceField(
        choices=[('M', 'Message'), ('F', 'File'), ('I', 'Image'), ('D', 'Document')],
        default='M'
    )
    recipient_id = serializers.IntegerField(required=False)
    recipient_type = serializers.ChoiceField(
        choices=[('staff', 'Staff'), ('consultant', 'Consultant')],
        required=False
    )
    file = serializers.FileField(required=False, help_text="Upload file (images or documents)")

    def validate(self, data):
        """Validate that either message or file is provided"""
        message = data.get('message', '').strip()
        file_name = data.get('file_name', '').strip()
        uploaded_file = self.context.get('request').FILES.get('file') if self.context.get('request') else None

        if not message and not file_name and not uploaded_file:
            raise serializers.ValidationError(
                "Either message, file_name, or uploaded file must be provided"
            )

        return data

    def validate_file(self, value):
        """Validate uploaded file"""
        if value:
            # Check file size (1MB for images, 5MB for documents)
            max_size = 1 * 1024 * 1024  # 1MB default

            if value.content_type.startswith('image/'):
                max_size = 1 * 1024 * 1024  # 1MB for images
                allowed_types = ['image/jpeg', 'image/jpg', 'image/png']
                if value.content_type not in allowed_types:
                    raise serializers.ValidationError(
                        "Invalid image type. Allowed: JPEG, JPG, PNG"
                    )
            else:
                max_size = 5 * 1024 * 1024  # 5MB for documents
                allowed_types = [
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ]
                if value.content_type not in allowed_types:
                    raise serializers.ValidationError(
                        "Invalid document type. Allowed: PDF, DOC, DOCX, XLS, XLSX"
                    )

            if value.size > max_size:
                max_size_mb = max_size / (1024 * 1024)
                raise serializers.ValidationError(
                    f"File too large. Maximum size: {max_size_mb}MB"
                )

        return value


class ChatHistorySerializer(serializers.Serializer):
    """
    Serializer for chat history requests
    Supports both consultant and staff user types with proper validation
    """
    room_id = serializers.CharField(required=False)
    user_id = serializers.IntegerField(required=False)
    user_consult_id = serializers.IntegerField(required=False)
    page = serializers.IntegerField(default=1, min_value=1)
    per_page = serializers.IntegerField(default=20, min_value=1, max_value=100)
    date_from = serializers.DateTimeField(required=False)
    date_to = serializers.DateTimeField(required=False)

    def validate(self, data):
        """
        Validate chat history request based on user type:
        - Consultants: No additional parameters required (use their own ID)
        - Staff: Must provide user_consult_id parameter
        """
        request = self.context.get('request')

        if not request or not hasattr(request, 'user') or not request.user:
            raise serializers.ValidationError("Authentication required")

        user = request.user

        if not hasattr(user, 'user_type'):
            raise serializers.ValidationError("User type not found")

        if user.user_type == 'consultant':
            # Consultant accessing their own room - no additional validation needed
            pass
        elif user.user_type == 'staff':
            # Staff must provide user_consult_id to specify which consultant's room
            user_consult_id = data.get('user_consult_id')
            if not user_consult_id:
                raise serializers.ValidationError({
                    'user_consult_id': ['This field is required for staff users to specify which consultant\'s chat room to access']
                })
        else:
            # Block any other user types (members, etc.)
            raise serializers.ValidationError("Access denied for this user type")

        return data


class OnlineStatusSerializer(serializers.Serializer):
    """
    Serializer for online status
    """
    user_id = serializers.IntegerField()
    user_type = serializers.ChoiceField(choices=[('staff', 'Staff'), ('consultant', 'Consultant')])
    is_online = serializers.BooleanField()
    last_seen = serializers.DateTimeField(allow_null=True)
    status_message = serializers.CharField(max_length=200, allow_blank=True, required=False)


class MobileChatRoomSerializer(serializers.Serializer):
    """
    Serializer for mobile chat rooms using existing TcdChat model
    """
    room_id = serializers.CharField()
    room_name = serializers.CharField()
    room_type = serializers.CharField(default='consultation')
    participant_info = serializers.ListField()
    last_message = serializers.DictField(allow_null=True)
    unread_count = serializers.IntegerField(default=0)
    created_at = serializers.DateTimeField()


class MobileChatSessionSerializer(serializers.Serializer):
    """
    Serializer for mobile chat sessions using existing models
    """
    session_id = serializers.CharField()
    user_id = serializers.IntegerField()
    user_type = serializers.CharField()
    is_online = serializers.BooleanField(default=True)
    last_seen = serializers.DateTimeField()
    device_token = serializers.CharField(required=False, allow_blank=True)


class StartChatSerializer(serializers.Serializer):
    """
    Serializer for starting a new chat session
    Updated for new room ownership model:
    - Consultants create their own room (no staff_id required)
    - Staff can join any consultant's room (consultant_id required)
    - Members are completely blocked
    """
    consultant_id = serializers.IntegerField(required=False)
    staff_id = serializers.IntegerField(required=False)  # For backward compatibility
    chat_type = serializers.ChoiceField(
        choices=[('consultation', 'Consultation'), ('support', 'Support')],
        default='consultation'
    )
    device_token = serializers.CharField(max_length=500, required=False)

    def validate(self, data):
        """Validate chat participants based on new access control"""
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'user_type'):
            raise serializers.ValidationError("User authentication required")

        user = request.user

        # Block members completely
        if user.user_type == 'member':
            raise serializers.ValidationError("Members are not allowed to access chat features")

        # Consultant validation - no staff_id required
        elif user.user_type == 'consultant':
            # Consultants can create their room without specifying staff
            # staff_id is optional for backward compatibility
            pass

        # Staff validation - consultant_id required
        else:
            # Assume this is staff (TcdUsers) trying to join consultant room
            if not data.get('consultant_id'):
                raise serializers.ValidationError("Consultant ID is required for staff to join room")

        return data


class PushNotificationSerializer(serializers.Serializer):
    """
    Serializer for push notification data
    """
    title = serializers.CharField(max_length=200)
    body = serializers.CharField(max_length=500)
    data = serializers.DictField(required=False)
    device_tokens = serializers.ListField(
        child=serializers.CharField(max_length=500),
        required=False
    )
