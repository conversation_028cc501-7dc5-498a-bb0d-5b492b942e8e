# -*- coding: utf-8 -*-
"""
Firebase Cloud Messaging (FCM) Push Notification Utility
"""
import os
import json
import logging
import requests
from django.conf import settings
from typing import List, Dict, Optional, Union

logger = logging.getLogger(__name__)


class FirebaseNotificationService:
    """
    Service class for sending Firebase Cloud Messaging (FCM) push notifications
    """
    
    # FCM API endpoint
    FCM_URL = "https://fcm.googleapis.com/fcm/send"
    
    def __init__(self):
        """
        Initialize Firebase notification service
        """
        self.server_key = os.environ.get('FCM_SERVER_KEY', '')
        if not self.server_key:
            logger.warning("FCM_SERVER_KEY not found in environment variables")
    
    def send_notification(
        self,
        device_tokens: Union[str, List[str]],
        title: str,
        body: str,
        data: Optional[Dict] = None,
        click_action: Optional[str] = None
    ) -> Dict:
        """
        Send push notification to device(s)
        
        Args:
            device_tokens: Single token string or list of device tokens
            title: Notification title
            body: Notification body
            data: Additional data payload
            click_action: URL to open when notification is clicked
            
        Returns:
            dict: Response with success status and details
        """
        try:
            if not self.server_key:
                return {
                    'success': False,
                    'error': 'FCM server key not configured',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            # Convert single token to list
            if isinstance(device_tokens, str):
                device_tokens = [device_tokens]
            
            # Filter out empty tokens
            valid_tokens = [token for token in device_tokens if token and token.strip()]
            
            if not valid_tokens:
                return {
                    'success': False,
                    'error': 'No valid device tokens provided',
                    'sent_count': 0,
                    'failed_count': 0
                }
            
            # Prepare notification payload
            notification_payload = {
                'title': title,
                'body': body,
                'sound': 'default',
                'badge': 1
            }
            
            # Add click action if provided
            if click_action:
                notification_payload['click_action'] = click_action
            
            # Prepare data payload
            data_payload = data or {}
            
            # Prepare headers
            headers = {
                'Authorization': f'key={self.server_key}',
                'Content-Type': 'application/json'
            }
            
            sent_count = 0
            failed_count = 0
            errors = []
            
            # Send notifications (batch processing for multiple tokens)
            if len(valid_tokens) == 1:
                # Single recipient
                payload = {
                    'to': valid_tokens[0],
                    'notification': notification_payload,
                    'data': data_payload,
                    'priority': 'high'
                }
                
                result = self._send_single_notification(payload, headers)
                if result['success']:
                    sent_count += 1
                else:
                    failed_count += 1
                    errors.append(result['error'])
                    
            else:
                # Multiple recipients
                payload = {
                    'registration_ids': valid_tokens,
                    'notification': notification_payload,
                    'data': data_payload,
                    'priority': 'high'
                }
                
                result = self._send_batch_notification(payload, headers)
                sent_count = result['sent_count']
                failed_count = result['failed_count']
                errors = result['errors']
            
            logger.info(f"FCM notification sent - Success: {sent_count}, Failed: {failed_count}")
            
            return {
                'success': sent_count > 0,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'errors': errors,
                'total_tokens': len(valid_tokens)
            }
            
        except Exception as e:
            logger.error(f"Error sending FCM notification: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'sent_count': 0,
                'failed_count': len(device_tokens) if isinstance(device_tokens, list) else 1
            }
    
    def _send_single_notification(self, payload: Dict, headers: Dict) -> Dict:
        """
        Send notification to single device
        
        Args:
            payload: FCM payload
            headers: Request headers
            
        Returns:
            dict: Response details
        """
        try:
            response = requests.post(
                self.FCM_URL,
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('success', 0) > 0:
                    return {'success': True, 'response': response_data}
                else:
                    error_msg = response_data.get('results', [{}])[0].get('error', 'Unknown error')
                    return {'success': False, 'error': error_msg}
            else:
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}: {response.text}'
                }
                
        except requests.exceptions.RequestException as e:
            return {'success': False, 'error': f'Request failed: {str(e)}'}
    
    def _send_batch_notification(self, payload: Dict, headers: Dict) -> Dict:
        """
        Send notification to multiple devices
        
        Args:
            payload: FCM payload
            headers: Request headers
            
        Returns:
            dict: Response details with counts
        """
        try:
            response = requests.post(
                self.FCM_URL,
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                sent_count = response_data.get('success', 0)
                failed_count = response_data.get('failure', 0)
                
                errors = []
                if 'results' in response_data:
                    for i, result in enumerate(response_data['results']):
                        if 'error' in result:
                            errors.append(f"Token {i}: {result['error']}")
                
                return {
                    'sent_count': sent_count,
                    'failed_count': failed_count,
                    'errors': errors
                }
            else:
                return {
                    'sent_count': 0,
                    'failed_count': len(payload.get('registration_ids', [])),
                    'errors': [f'HTTP {response.status_code}: {response.text}']
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'sent_count': 0,
                'failed_count': len(payload.get('registration_ids', [])),
                'errors': [f'Request failed: {str(e)}']
            }
    
    def send_project_matching_notification(
        self,
        device_token: str,
        project_name: str,
        consultant_name: str = None,
        project_id: int = None
    ) -> Dict:
        """
        Send project matching notification to project owner
        
        Args:
            device_token: Device token of project owner
            project_name: Name of the project
            consultant_name: Name of interested consultant (optional)
            project_id: ID of the project for deep linking
            
        Returns:
            dict: Response details
        """
        title = "ศูนย์ข้อมูลที่ปรึกษา"
        body = "ท่านมีที่ปรึกษาที่สนใจในโครงการที่ประกาศ"
        
        # Add project name to body if available
        if project_name:
            body = f"ท่านมีที่ปรึกษาที่สนใจในโครงการ \"{project_name}\""
        
        # Prepare data for deep linking
        data = {
            'type': 'project_matching',
            'action': 'view_matching_results'
        }
        
        if project_id:
            data['project_id'] = str(project_id)
        
        if consultant_name:
            data['consultant_name'] = consultant_name
        
        # URL for matching results page (adjust based on your mobile app routing)
        click_action = f"app://matching-results"
        if project_id:
            click_action = f"app://matching-results?project_id={project_id}"
        
        return self.send_notification(
            device_tokens=device_token,
            title=title,
            body=body,
            data=data,
            click_action=click_action
        )


# Global instance for easy access
firebase_service = FirebaseNotificationService()


def send_push_notification(
    device_tokens: Union[str, List[str]],
    title: str,
    body: str,
    data: Optional[Dict] = None,
    click_action: Optional[str] = None
) -> Dict:
    """
    Convenience function to send push notification
    
    Args:
        device_tokens: Single token string or list of device tokens
        title: Notification title
        body: Notification body
        data: Additional data payload
        click_action: URL to open when notification is clicked
        
    Returns:
        dict: Response with success status and details
    """
    return firebase_service.send_notification(
        device_tokens=device_tokens,
        title=title,
        body=body,
        data=data,
        click_action=click_action
    )


def send_project_matching_notification(
    device_token: str,
    project_name: str,
    consultant_name: str = None,
    project_id: int = None
) -> Dict:
    """
    Convenience function to send project matching notification
    
    Args:
        device_token: Device token of project owner
        project_name: Name of the project
        consultant_name: Name of interested consultant (optional)
        project_id: ID of the project for deep linking
        
    Returns:
        dict: Response details
    """
    return firebase_service.send_project_matching_notification(
        device_token=device_token,
        project_name=project_name,
        consultant_name=consultant_name,
        project_id=project_id
    )
