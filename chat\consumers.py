# -*- coding: utf-8 -*-
"""
WebSocket consumers for real-time chat functionality
"""
import json
import logging
import os
from decimal import Decimal
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist
from django.conf import settings

from authentication.models import TcdAppMember, TcdUserConsult
from MCDC.models import TcdUserConsult as UserConsult, TcdUsers
from .models import TcdChat
from .serializers import ChatMessageSerializer
from .auth import (
    validate_websocket_chat_access,
    validate_room_access,
    validate_message_permission,
    get_websocket_error_response
)
from .utils import save_websocket_file, get_file_url, get_full_url_for_message

logger = logging.getLogger(__name__)


def decimal_to_json_serializable(obj):
    """
    Convert Decimal objects to JSON serializable format
    """
    if isinstance(obj, dict):
        return {key: decimal_to_json_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [decimal_to_json_serializable(item) for item in obj]
    elif isinstance(obj, Decimal):
        # Convert Decimal to int if it has no decimal places, otherwise to float
        if obj % 1 == 0:
            return int(obj)
        else:
            return float(obj)
    else:
        return obj


class ChatConsumer(AsyncWebsocketConsumer):
    """
    General chat consumer for group chat rooms (Legacy - with basic authentication)
    """

    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'
        self.user = self.scope.get('user')

        # Check authentication for legacy consumer
        if not self.user or not self.user.is_authenticated:
            logger.warning(f"Unauthenticated WebSocket connection attempt to legacy room: {self.room_name}")
            await self.close(code=4001)  # Unauthorized
            return

        # Basic chat access validation
        is_valid, error_message = validate_websocket_chat_access(self.user)
        if not is_valid:
            logger.warning(f"Chat access denied for user {self.user.username}: {error_message}")
            await self.close(code=4003)  # Forbidden
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"Authenticated WebSocket connected to legacy room: {self.room_name} - User: {self.user.username}")
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        logger.info(f"WebSocket disconnected from room: {self.room_name}")
    
    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'chat_message')
            
            if message_type == 'chat_message':
                await self.handle_chat_message(text_data_json)
            elif message_type == 'typing':
                await self.handle_typing(text_data_json)
            elif message_type == 'read_status':
                await self.handle_read_status(text_data_json)
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received")
            await self.send(text_data=json.dumps({
                'error': 'Invalid JSON format'
            }))
        except Exception as e:
            logger.error(f"Error in receive: {str(e)}")
            await self.send(text_data=json.dumps({
                'error': 'Internal server error'
            }))
    
    async def handle_chat_message(self, data):
        message = data.get('message', '')
        user_id = data.get('user_id')
        user_type = data.get('user_type', 'member')
        file_name = data.get('file_name')
        file_src = data.get('file_src')
        
        if not message and not file_name:
            return
        
        # Save message to database
        chat_message = await self.save_message(
            message=message,
            user_id=user_id,
            user_type=user_type,
            file_name=file_name,
            file_src=file_src
        )
        
        if chat_message:
            # Send message to room group
            # Ensure all data is JSON serializable before sending
            subdirectory = getattr(settings, 'CHAT_SUB_DIR', '')
            message_data = decimal_to_json_serializable({
                'type': 'chat_message',
                'message': message,
                'user_id': user_id,
                'user_type': user_type,
                'file_name': file_name,
                'file_src': file_src,
                'full_url': get_full_url_for_message(file_src, subdirectory),
                'timestamp': chat_message['timestamp'],
                'message_id': chat_message['id']
            })
            await self.channel_layer.group_send(
                self.room_group_name,
                message_data
            )
    
    async def handle_typing(self, data):
        user_id = data.get('user_id')
        is_typing = data.get('is_typing', False)
        
        # Send typing indicator to room group
        # Ensure all data is JSON serializable before sending
        typing_data = decimal_to_json_serializable({
            'type': 'typing_indicator',
            'user_id': user_id,
            'is_typing': is_typing
        })
        await self.channel_layer.group_send(
            self.room_group_name,
            typing_data
        )
    
    async def handle_read_status(self, data):
        message_id = data.get('message_id')
        user_id = data.get('user_id')
        user_type = data.get('user_type', 'member')
        
        # Update read status
        await self.update_read_status(message_id, user_id, user_type)
        
        # Send read status to room group
        # Ensure all data is JSON serializable before sending
        status_data = decimal_to_json_serializable({
            'type': 'read_status_update',
            'message_id': message_id,
            'user_id': user_id,
            'user_type': user_type
        })
        await self.channel_layer.group_send(
            self.room_group_name,
            status_data
        )
    
    # Receive message from room group
    async def chat_message(self, event):
        # Convert any Decimal values to JSON serializable format
        # Convert file_src to full URL if exists
        file_src = event.get('file_src')
        if file_src:
            file_src = get_file_url(file_src)

        data = decimal_to_json_serializable({
            'type': 'chat_message',
            'message': event['message'],
            'user_id': event['user_id'],
            'user_type': event['user_type'],
            'file_name': event.get('file_name'),
            'file_src': file_src,
            'timestamp': event['timestamp'],
            'message_id': event['message_id']
        })
        await self.send(text_data=json.dumps(data))
    
    async def typing_indicator(self, event):
        # Convert any Decimal values to JSON serializable format
        data = decimal_to_json_serializable({
            'type': 'typing',
            'user_id': event['user_id'],
            'is_typing': event['is_typing']
        })
        await self.send(text_data=json.dumps(data))
    
    async def read_status_update(self, event):
        # Convert any Decimal values to JSON serializable format
        data = decimal_to_json_serializable({
            'type': 'read_status',
            'message_id': event['message_id'],
            'user_id': event['user_id'],
            'user_type': event['user_type']
        })
        await self.send(text_data=json.dumps(data))
    
    @database_sync_to_async
    def save_message(self, message, user_id, user_type, file_name=None, file_src=None):
        try:
            # This is a simplified version - you'll need to adapt based on your TcdChat model requirements
            # For now, we'll return a mock response
            return {
                'id': 1,
                'timestamp': timezone.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error saving message: {str(e)}")
            return None
    
    @database_sync_to_async
    def update_read_status(self, message_id, user_id, user_type):
        try:
            # Update read status logic here
            pass
        except Exception as e:
            logger.error(f"Error updating read status: {str(e)}")


class PrivateChatConsumer(ChatConsumer):
    """
    Private chat consumer for one-on-one conversations (Legacy - with authentication)
    """

    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.room_group_name = f'private_chat_{self.user_id}'
        self.user = self.scope.get('user')

        # Check authentication
        if not self.user or not self.user.is_authenticated:
            logger.warning(f"Unauthenticated WebSocket connection attempt to private chat: {self.user_id}")
            await self.close(code=4001)  # Unauthorized
            return

        # Validate chat access
        is_valid, error_message = validate_websocket_chat_access(self.user)
        if not is_valid:
            logger.warning(f"Private chat access denied for user {self.user.username}: {error_message}")
            await self.close(code=4003)  # Forbidden
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"Authenticated private chat connected - User: {self.user.username}, Target: {self.user_id}")


class ConsultChatConsumer(ChatConsumer):
    """
    Consultation chat consumer for member-consultant conversations (Legacy - with authentication)
    """

    async def connect(self):
        self.consult_id = self.scope['url_route']['kwargs']['consult_id']
        self.room_group_name = f'consult_chat_{self.consult_id}'
        self.user = self.scope.get('user')

        # Check authentication
        if not self.user or not self.user.is_authenticated:
            logger.warning(f"Unauthenticated WebSocket connection attempt to consultation: {self.consult_id}")
            await self.close(code=4001)  # Unauthorized
            return

        # Validate chat access
        is_valid, error_message = validate_websocket_chat_access(self.user)
        if not is_valid:
            logger.warning(f"Consultation chat access denied for user {self.user.username}: {error_message}")
            await self.close(code=4003)  # Forbidden
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"Authenticated consultation chat connected - User: {self.user.username}, Consult: {self.consult_id}")


class MobileChatConsumer(AsyncWebsocketConsumer):
    """
    Mobile chat consumer for room-based conversations with authentication
    """

    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'mobile_chat_{self.room_id}'
        self.user = self.scope.get('user')

        # Check authentication
        if not self.user or not self.user.is_authenticated:
            logger.warning(f"Unauthenticated WebSocket connection attempt to room: {self.room_id}")
            await self.close(code=4001)  # Unauthorized
            return

        # Validate chat access (block members, allow consultants and staff)
        is_valid, error_message = validate_websocket_chat_access(self.user)
        if not is_valid:
            logger.warning(f"Chat access denied for user {self.user.username}: {error_message}")
            await self.send(text_data=json.dumps(
                get_websocket_error_response('access_denied', error_message)
            ))
            await self.close(code=4003)  # Forbidden
            return

        # Validate room access
        room_valid, room_error, consultant_id = await validate_room_access(self.user, self.room_id)
        if not room_valid:
            logger.warning(f"Room access denied for user {self.user.username} to room {self.room_id}: {room_error}")
            await self.send(text_data=json.dumps(
                get_websocket_error_response('room_access_denied', room_error)
            ))
            await self.close(code=4003)  # Forbidden
            return

        # Store consultant_id for later use
        self.consultant_id = consultant_id

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"Authenticated mobile chat connected - User: {self.user.username} ({self.user.user_type}), Room: {self.room_id}")

        # Send connection confirmation
        data = decimal_to_json_serializable({
            'type': 'connection_established',
            'room_id': self.room_id,
            'user_type': self.user.user_type,
            'message': 'Successfully connected to chat room'
        })
        await self.send(text_data=json.dumps(data))

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
        logger.info(f"Mobile chat disconnected from room: {self.room_id}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message_type = data.get('type', 'message')

            if message_type == 'message':
                await self.handle_message(data)
            elif message_type == 'typing':
                await self.handle_typing(data)
            elif message_type == 'read_receipt':
                await self.handle_read_receipt(data)
            elif message_type == 'join_room':
                await self.handle_join_room(data)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
        except Exception as e:
            logger.error(f"Error in mobile chat receive: {str(e)}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Internal server error'
            }))

    async def handle_message(self, data):
        """Handle incoming message with authorization"""
        message_text = data.get('message', '')
        is_prompt_answer = data.get('is_prompt_answer', False)
        recipient_id = data.get('recipient_id')  # Optional for targeted messages
        file_data = data.get('file_data')

        # Handle alternative file format (for backward compatibility)
        file_name = data.get('file_name')
        file_src = data.get('file_src')

        # Log the received data for debugging
        logger.info(f"Received message data: message='{message_text}', file_data={file_data}, file_name='{file_name}', file_src='{file_src}'")

        # Validate message permission
        permission_valid, permission_error, routing_info = await validate_message_permission(
            self.user, self.room_id, recipient_id
        )

        if not permission_valid:
            logger.warning(f"Message permission denied for user {self.user.username}: {permission_error}")
            await self.send(text_data=json.dumps(
                get_websocket_error_response('message_permission_denied', permission_error)
            ))
            return

        # Validate message content - check for file_data OR file_name
        has_file_data = file_data and isinstance(file_data, dict) and file_data.get('name') and file_data.get('data')
        has_file_info = file_name and file_src

        if not message_text and not has_file_data and not has_file_info:
            await self.send(text_data=json.dumps(
                get_websocket_error_response('invalid_message', 'Message content or file is required')
            ))
            return

        # If we have file_name but no file_data, this might be a legacy format or missing file content
        if file_name and not has_file_data:
            logger.warning(f"Received file_name '{file_name}' but no file_data. This might indicate missing file content.")
            # We'll still process the message but without actual file saving
            file_data = None

        # Save message to database with proper routing
        message_data = await self.save_mobile_message(
            room_id=self.room_id,
            message=message_text,
            user_id=self.user.id,
            user_type=self.user.user_type,
            file_data=file_data,
            routing_info=routing_info,
            legacy_file_name=file_name,
            legacy_file_src=file_src,
            is_prompt_answer=is_prompt_answer
        )

        if message_data:
            # Broadcast to room
            # Ensure all data is JSON serializable before sending
            serializable_data = decimal_to_json_serializable(message_data)
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message_data': serializable_data
                }
            )
        else:
            await self.send(text_data=json.dumps(
                get_websocket_error_response('server_error', 'Failed to save message')
            ))

    async def handle_typing(self, data):
        """Handle typing indicator"""
        # Ensure all data is JSON serializable before sending
        typing_data = decimal_to_json_serializable({
            'type': 'typing_indicator',
            'user_id': data.get('user_id'),
            'user_type': data.get('user_type'),
            'is_typing': data.get('is_typing', False)
        })
        await self.channel_layer.group_send(
            self.room_group_name,
            typing_data
        )

    async def handle_read_receipt(self, data):
        """Handle read receipt"""
        message_id = data.get('message_id')
        user_id = data.get('user_id')
        user_type = data.get('user_type')

        # Update read status
        await self.update_message_read_status(message_id, user_id, user_type)

        # Broadcast read receipt
        # Ensure all data is JSON serializable before sending
        receipt_data = decimal_to_json_serializable({
            'type': 'read_receipt',
            'message_id': message_id,
            'user_id': user_id,
            'user_type': user_type
        })
        await self.channel_layer.group_send(
            self.room_group_name,
            receipt_data
        )

    async def handle_join_room(self, data):
        """Handle user joining room"""
        user_id = data.get('user_id')
        user_type = data.get('user_type')

        # Update online status
        await self.update_user_online_status(user_id, user_type, True)

        # Notify others
        # Ensure all data is JSON serializable before sending
        join_data = decimal_to_json_serializable({
            'type': 'user_joined',
            'user_id': user_id,
            'user_type': user_type
        })
        await self.channel_layer.group_send(
            self.room_group_name,
            join_data
        )

    # WebSocket event handlers
    async def chat_message(self, event):
        """Send message to WebSocket"""
        # Convert any Decimal values to JSON serializable format
        message_data = decimal_to_json_serializable(event['message_data'])
        await self.send(text_data=json.dumps({
            'type': 'message',
            'data': message_data
        }))

    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket"""
        # Convert any Decimal values to JSON serializable format
        data = decimal_to_json_serializable({
            'type': 'typing',
            'user_id': event['user_id'],
            'user_type': event['user_type'],
            'is_typing': event['is_typing']
        })
        await self.send(text_data=json.dumps(data))

    async def read_receipt(self, event):
        """Send read receipt to WebSocket"""
        # Convert any Decimal values to JSON serializable format
        data = decimal_to_json_serializable({
            'type': 'read_receipt',
            'message_id': event['message_id'],
            'user_id': event['user_id'],
            'user_type': event['user_type']
        })
        await self.send(text_data=json.dumps(data))

    async def user_joined(self, event):
        """Send user joined notification to WebSocket"""
        # Convert any Decimal values to JSON serializable format
        data = decimal_to_json_serializable({
            'type': 'user_joined',
            'user_id': event['user_id'],
            'user_type': event['user_type']
        })
        await self.send(text_data=json.dumps(data))

    # Database operations
    @database_sync_to_async
    def save_mobile_message(self, room_id, message, user_id, user_type, file_data=None, routing_info=None, legacy_file_name=None, legacy_file_src=None, is_prompt_answer=False):
        """Save message to database using existing TcdChat model with proper authorization"""
        try:
            if not routing_info:
                logger.error("Missing routing_info for message save")
                return None

            # Handle file upload if provided
            file_name = None
            file_src = None
            message_type = 'M'  # Default to message
            message_text = message  # Store original message

            if file_data:
                # Save file to filesystem using the new utility function
                logger.info(f"Processing file_data: {file_data}")
                file_save_result = save_websocket_file(file_data)

                if file_save_result['success']:
                    file_name = file_save_result['file_name']  # Original filename
                    file_src = file_save_result['file_path']   # Relative path for database
                    message_type = file_save_result['message_type']  # 'I' or 'D'
                    message_text = None  # Set message to null when sending files via WebSocket
                    logger.info(f"File saved successfully: {file_src}")
                else:
                    logger.error(f"Failed to save file: {file_save_result['error']}")
                    # Still create the message but without file data
                    file_name = file_data.get('name', 'Unknown file')
                    file_src = None
                    message_type = 'M'  # Fallback to message
            elif legacy_file_name:
                # Handle legacy file format (file already uploaded or referenced)
                logger.info(f"Processing legacy file format: file_name='{legacy_file_name}', file_src='{legacy_file_src}'")
                file_name = legacy_file_name
                file_src = legacy_file_src

                # Determine message type based on file extension
                if file_name:
                    file_extension = os.path.splitext(file_name)[1].lower()
                    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
                    if file_extension in image_extensions:
                        message_type = 'I'
                    else:
                        message_type = 'D'
                    message_text = None  # Set message to null when sending files via legacy format
                else:
                    message_type = 'M'

            # Set read status based on user type (same logic as send_message in views)
            if user_type == 'consultant':
                # Consultant sending message - mark as read for consultant
                consult_read = '1'  # Read (consultant sent this message)
                users_read = '0'    # Unread for staff
                if is_prompt_answer:
                    users_read = '1'
            else:
                # Staff sending message - mark as read for staff
                consult_read = '0'  # Unread for consultant
                users_read = '1'    # Read (staff sent this message)

            user_consult_id = routing_info['user_consult_id']
            users_id = routing_info['users_id']
            if is_prompt_answer:
                users_id = 1

            # Create message with validated routing information
            chat_message = TcdChat.objects.create(
                type=message_type,
                message=message_text,
                file_name=file_name,
                file_src=file_src,
                user_consult_id=user_consult_id,
                users_id=users_id,
                consult_read=consult_read,
                users_read=users_read,
                date=timezone.now()
            )

            # Get sender information for response
            sender_name = 'Unknown'
            try:
                if is_prompt_answer:
                    user_type = 'staff'
                    staff = TcdUsers.objects.get(id=users_id)
                    sender_name = f"{staff.firstname} {staff.lastname}".strip() or staff.username
                else:   
                    if user_type == 'consultant':
                        consultant = TcdUserConsult.objects.get(id=user_id)
                        sender_name = getattr(consultant, 'name', consultant.username)
                    elif user_type == 'staff':
                        staff = TcdUsers.objects.get(id=user_id)
                        sender_name = f"{staff.firstname} {staff.lastname}".strip() or staff.username
                    
            except (TcdUserConsult.DoesNotExist, TcdUsers.DoesNotExist):
                pass

            # Ensure all values are JSON serializable
            # Convert file_src to full URL if file exists
            file_src_url = None
            full_url = None
            subdirectory = getattr(settings, 'CHAT_SUB_DIR', '')
            if chat_message.file_src:
                file_src_url = get_file_url(chat_message.file_src)
                full_url = get_full_url_for_message(chat_message.file_src, subdirectory) if chat_message.file_src else None

            message_data = {
                'id': chat_message.id,
                'message': chat_message.message,
                'file_name': chat_message.file_name,
                'file_src': file_src_url,
                'full_url': full_url,
                'user_id': user_id,
                'user_type': user_type,
                'sender_name': sender_name,
                'timestamp': chat_message.date.isoformat(),
                'room_id': room_id,
                'consultant_id': routing_info['consultant_id']
            }

            return decimal_to_json_serializable(message_data)

        except Exception as e:
            logger.error(f"Error saving mobile message: {str(e)}")
            return None

    @database_sync_to_async
    def update_message_read_status(self, message_id, user_id, user_type):
        """Update message read status"""
        try:
            if user_type == 'staff':
                TcdChat.objects.filter(
                    id=message_id,
                    users_id=user_id
                ).update(users_read='1')  # Read status
            elif user_type == 'consultant':
                TcdChat.objects.filter(
                    id=message_id,
                    user_consult_id=user_id
                ).update(consult_read='1')  # Read status
        except Exception as e:
            logger.error(f"Error updating read status: {str(e)}")

    @database_sync_to_async
    def update_user_online_status(self, user_id, user_type, is_online):
        """Update user online status (simplified for existing models)"""
        try:
            # For now, just log the status change since we don't have session tracking
            logger.info(f"User {user_id} ({user_type}) online status: {is_online}")
            # In a real implementation, you might use cache or another mechanism
        except Exception as e:
            logger.error(f"Error updating online status: {str(e)}")


class ChatSessionConsumer(AsyncWebsocketConsumer):
    """
    Chat session consumer for individual user sessions with authentication
    """

    async def connect(self):
        self.session_id = self.scope['url_route']['kwargs']['session_id']
        self.session_group_name = f'chat_session_{self.session_id}'
        self.user = self.scope.get('user')

        # Check authentication
        if not self.user or not self.user.is_authenticated:
            logger.warning(f"Unauthenticated WebSocket connection attempt to session: {self.session_id}")
            await self.close(code=4001)  # Unauthorized
            return

        # Validate chat access
        is_valid, error_message = validate_websocket_chat_access(self.user)
        if not is_valid:
            logger.warning(f"Chat access denied for user {self.user.username}: {error_message}")
            await self.send(text_data=json.dumps(
                get_websocket_error_response('access_denied', error_message)
            ))
            await self.close(code=4003)  # Forbidden
            return

        # Join session group
        await self.channel_layer.group_add(
            self.session_group_name,
            self.channel_name
        )

        await self.accept()
        logger.info(f"Authenticated chat session connected - User: {self.user.username} ({self.user.user_type}), Session: {self.session_id}")

    async def disconnect(self, close_code):
        # Leave session group
        await self.channel_layer.group_discard(
            self.session_group_name,
            self.channel_name
        )

        # Update offline status
        await self.update_session_offline()
        logger.info(f"Chat session disconnected: {self.session_id}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            # Handle session-specific messages
            await self.handle_session_message(data)
        except Exception as e:
            logger.error(f"Error in session receive: {str(e)}")

    async def handle_session_message(self, data):
        """Handle session-specific messages"""
        # Implementation for session-specific functionality
        pass

    @database_sync_to_async
    def update_session_offline(self):
        """Update session to offline (simplified for existing models)"""
        try:
            # For now, just log the session disconnect since we don't have session tracking
            logger.info(f"Session {self.session_id} went offline")
            # In a real implementation, you might use cache or another mechanism
        except Exception as e:
            logger.error(f"Error updating session offline: {str(e)}")
