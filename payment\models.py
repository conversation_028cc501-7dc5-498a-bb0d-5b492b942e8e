from django.db import models


class TcdPayment(models.Model):
    request_type = models.IntegerField(blank=True, null=True)
    number = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    ref2 = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    biller_id = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    bill_no = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    cgd_ref1 = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    cgd_ref2 = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    barcode_string = models.Char<PERSON>ield(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    qrcode_string = models.CharField(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    response_pmt1 = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    response_pmt2 = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    src_qrcode = models.CharField(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    src_payin = models.CharField(max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    amount = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    create_date = models.DateTimeField(blank=True, null=True)
    expire_date = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=1, db_collation='Thai_CI_AI', blank=True, null=True)
    src = models.TextField(db_collation='Thai_CI_AI', blank=True, null=True)
    pay_date = models.DateTimeField(blank=True, null=True)
    pay_amount = models.CharField(max_length=50, db_collation='Thai_CI_AI', blank=True, null=True)
    worklist_id = models.IntegerField(blank=True, null=True)
    user_consult_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tcd_payment'

