# -*- coding: utf-8 -*-
"""
Search Services Module

This module contains service classes for search-related functionality,
including master data services for sectors, skills, and services.
"""
import logging

from .models import TcdSector, TcdSkill, TcdService
from utils.response import service_success_response, service_error_response

logger = logging.getLogger(__name__)


class SearchService:
    """
    Service class for search-related functionality
    """

    @staticmethod
    def get_sectors_by_ids(sector_ids, language='th'):
        """
        Get sectors by IDs
        
        Args:
            sector_ids (list): List of sector IDs
            language (str): Language preference
            
        Returns:
            dict: Service response with sector data
        """
        try:
            if not sector_ids:
                return service_error_response(2000, language)  # Invalid parameter
            
            sectors = TcdSector.objects.filter(id__in=sector_ids).order_by('id')
            
            sector_data = []
            for sector in sectors:
                sector_data.append({
                    'id': sector.id,
                    'code': sector.code,
                    'name_th': sector.name_th,
                    'name_en': sector.name_en,
                    'display_text': f"{sector.code or ''} : {sector.name_th or ''} : {sector.name_en or ''}".strip(' :')
                })
            
            return service_success_response(sector_data, language)
            
        except Exception as e:
            logger.error(f"Error getting sectors by IDs: {str(e)}")
            return service_error_response(5000, language)  # System error

    @staticmethod
    def get_skills_by_sector_ids(sector_ids, language='th'):
        """
        Get skills by sector IDs
        
        Args:
            sector_ids (list): List of sector IDs
            language (str): Language preference
            
        Returns:
            dict: Service response with skill data
        """
        try:
            if not sector_ids:
                return service_error_response(2000, language)  # Invalid parameter
            
            skills = TcdSkill.objects.filter(
                sector_id__in=sector_ids
            ).order_by('st', 'nd', 'rd')
            
            skill_data = []
            for skill in skills:
                # Get sector info
                sector_info = None
                if skill.sector_id:
                    try:
                        sector = TcdSector.objects.get(id=skill.sector_id)
                        sector_info = {
                            'id': sector.id,
                            'code': sector.code,
                            'name_th': sector.name_th,
                            'name_en': sector.name_en,
                            'display_text': f"{sector.code or ''} : {sector.name_th or ''} : {sector.name_en or ''}".strip(' :')
                        }
                    except TcdSector.DoesNotExist:
                        pass
                
                skill_data.append({
                    'id': skill.id,
                    'sector_id': skill.sector_id,
                    'code': skill.code,
                    'name_th': skill.name_th,
                    'name_en': skill.name_en,
                    'st': skill.st,
                    'nd': skill.nd,
                    'rd': skill.rd,
                    'display_text': f"{skill.code or ''} : {skill.name_th or ''} : {skill.name_en or ''}".strip(' :'),
                    'sector_info': sector_info
                })
            
            return service_success_response(skill_data, language)
            
        except Exception as e:
            logger.error(f"Error getting skills by sector IDs: {str(e)}")
            return service_error_response(5000, language)  # System error

    @staticmethod
    def get_all_services(language='th'):
        """
        Get all services
        
        Args:
            language (str): Language preference
            
        Returns:
            dict: Service response with service data
        """
        try:
            services = TcdService.objects.all().order_by('code')
            
            service_data = []
            for service in services:
                service_data.append({
                    'id': service.id,
                    'code': service.code,
                    'name_th': service.name_th,
                    'name_en': service.name_en,
                    'display_text': f"{service.code or ''} : {service.name_th or ''} : {service.name_en or ''}".strip(' :')
                })
            
            return service_success_response(service_data, language)
            
        except Exception as e:
            logger.error(f"Error getting all services: {str(e)}")
            return service_error_response(5000, language)  # System error

    @staticmethod
    def validate_sector_ids(sector_ids_str):
        """
        Validate and parse sector IDs from string
        
        Args:
            sector_ids_str (str): Comma-separated sector IDs
            
        Returns:
            tuple: (is_valid, sector_ids_list, error_message)
        """
        try:
            if not sector_ids_str or not sector_ids_str.strip():
                return False, [], "Sector IDs are required"
            
            sector_ids = []
            for id_str in sector_ids_str.split(','):
                id_str = id_str.strip()
                if id_str:
                    try:
                        sector_id = int(id_str)
                        if sector_id <= 0:
                            return False, [], "All sector IDs must be positive integers"
                        sector_ids.append(sector_id)
                    except ValueError:
                        return False, [], "Invalid sector ID format"
            
            if not sector_ids:
                return False, [], "At least one valid sector ID is required"
            
            if len(sector_ids) > 50:
                return False, [], "Too many sector IDs (maximum 50)"
            
            return True, sector_ids, None
            
        except Exception as e:
            logger.error(f"Error validating sector IDs: {str(e)}")
            return False, [], "Invalid sector IDs format"
