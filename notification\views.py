from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from django.core.exceptions import ValidationError
import logging
from django.utils import timezone
from datetime import datetime
import pytz
from collections import defaultdict
from django.db.models import Q
from utils.response import APIResponse, get_language_from_request
from drf_spectacular.utils import extend_schema
import locale
from dateutil import parser as date_parser

from .models import TcdAppNotification, TcdNotiConsult
from .serializers import TcdAppNotificationSerializer, TcdNotiConsultSerializer
from utils.pagination import CustomPagination
from .fcm_service import send_push_notification

logger = logging.getLogger(__name__)


@extend_schema(
    summary="Get notifications",
    description="Get notifications with optional filtering",
    responses={
        status.HTTP_200_OK: TcdAppNotificationSerializer(many=True),
    },
    tags=['Notification']
)
class TcdAppNotificationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for TcdAppNotification model
    Provides CRUD operations for notifications
    """

    queryset = TcdAppNotification.objects.all().order_by('-create_date')
    serializer_class = TcdAppNotificationSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [IsAuthenticated]

    def list(self, request, *args, **kwargs):
        # Set locale for Thai month names
        try:
            locale.setlocale(locale.LC_TIME, 'th_TH.UTF-8')
        except:
            pass  # fallback if locale not available

        # Get query params
        noti_type = request.GET.get('noti_type')
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))

        queryset = self.get_queryset()
        
        # Add condition for TcdAppNotification.type filtering
        # Check TcdAppNotification.type against JWT user type and main_id
        if request.user and hasattr(request.user, 'id'):
            user_id = request.user.id
            user_type = getattr(request.user, 'user_type', None)
            
            # Build filter conditions based on user type from JWT
            if user_type == 'consultant':
                queryset = queryset.filter(type='APPCONSULTANT', main_id=user_id)
            elif user_type == 'member':
                logger.info(f"user_id: {user_id}")
                queryset = queryset.filter(type='APPMEMBER', main_id=user_id)
        
        # Handle different notification types
        if noti_type == 'service':
            if user_type != 'consultant':
                return APIResponse.error(error_code=1006,status_code=status.HTTP_401_UNAUTHORIZED,language=get_language_from_request(request))
            
            # Get service notifications for consultant
            noti_consult_service = TcdNotiConsult.objects.filter(user_consult_id=user_id).order_by('-date')
            
            # Pagination for service notifications
            total = noti_consult_service.count()
            start = (page - 1) * page_size
            end = start + page_size
            noti_consult_service = noti_consult_service[start:end]
            has_next = end < total

            # Serialize service notifications
            service_serializer = TcdNotiConsultSerializer(noti_consult_service, many=True)
            service_notifications = service_serializer.data

            # Add noti_type to service notifications
            for n in service_notifications:
                n['noti_type'] = 'service'
                n['is_read'] = True if n['read'] == '1' else False
                n['create_date'] = n['date'] if n['date'] else n['create_date']
            
            # Group service notifications by date
            groups = defaultdict(list)
            for n in service_notifications:
                dt_str = n['date']
                if isinstance(dt_str, str) and 'T' in dt_str:
                    date_key = dt_str.split('T')[0]
                else:
                    date_key = str(dt_str)
                groups[date_key].append(n)

            # Prepare response data for service notifications
            data = {
                'groups': [
                    {'date': date, 'notifications': items}
                    for date, items in groups.items()
                ]
            }
            
        else:
            # Handle regular notifications or combined notifications for consultant
            if noti_type:
                queryset = queryset.filter(noti_type=noti_type)
            
            # For consultant, combine regular notifications with service notifications if no specific type
            if user_type == 'consultant' and not noti_type:
                # Get service notifications
                noti_consult_service = TcdNotiConsult.objects.filter(user_consult_id=user_id).order_by('-date')
                service_serializer = TcdNotiConsultSerializer(noti_consult_service, many=True)
                service_notifications = service_serializer.data
                
                # Get regular notifications
                regular_serializer = self.get_serializer(queryset, many=True)
                regular_notifications = regular_serializer.data
                
                # Combine and sort all notifications by date
                all_notifications = []
                
                # Add regular notifications with type indicator
                for n in regular_notifications:
                    all_notifications.append(n)
                
                # Add service notifications with type indicator
                for n in service_notifications:
                    n['noti_type'] = 'service'
                    n['is_read'] = True if n['read'] == '1' else False
                    n['create_date'] = n['date'] if n['date'] else n['create_date']
                    all_notifications.append(n)
                
                # Sort by date (newest first)
                all_notifications.sort(key=lambda x: x.get('create_date', x.get('date', '')), reverse=True)
                
                # Apply pagination to combined results
                total = len(all_notifications)
                start = (page - 1) * page_size
                end = start + page_size
                paginated_notifications = all_notifications[start:end]
                has_next = end < total
                
                # Group combined notifications by date
                groups = defaultdict(list)
                for n in paginated_notifications:
                    dt_str = n.get('create_date', n.get('date', ''))
                    if isinstance(dt_str, str) and 'T' in dt_str:
                        date_key = dt_str.split('T')[0]
                    else:
                        date_key = str(dt_str)
                    groups[date_key].append(n)
                
                # Prepare response data for combined notifications
                data = {
                    'groups': [
                        {'date': date, 'notifications': items}
                        for date, items in groups.items()
                    ]
                }
                
            else:
                # Handle regular notifications only
                # Pagination for regular notifications
                total = queryset.count()
                start = (page - 1) * page_size
                end = start + page_size
                queryset = queryset[start:end]
                has_next = end < total

                # Serialize regular notifications
                serializer = self.get_serializer(queryset, many=True)
                notifications = serializer.data

                # Group regular notifications by date
                groups = defaultdict(list)
                for n in notifications:
                    dt_str = n['create_date']
                    if isinstance(dt_str, str) and 'T' in dt_str:
                        date_key = dt_str.split('T')[0]
                    else:
                        date_key = str(dt_str)
                    groups[date_key].append(n)

                # Prepare response data for regular notifications
                data = {
                    'groups': [
                        {'date': date, 'notifications': items}
                        for date, items in groups.items()
                    ]
                }
        
        # Create response with pagination at root level
        response_data = {
            'success': True,
            'error_code': None,
            'error_message': None,
            'data': data,
            'page': page,
            'per_page': page_size,
            'total': total,
            'has_next': has_next,
            'api_version': 'v.0.0.1'
        }
        
        return Response(response_data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        # Get notification by id
        notification = self.get_object()
        if notification.main_id:
            # Check TcdAppNotification.type against JWT user type and main_id
            if request.user and hasattr(request.user, 'id'):
                user_id = request.user.id
                user_type = getattr(request.user, 'user_type', None)
                if user_type == 'consultant':
                    if notification.type != 'APPCONSULTANT' or notification.main_id != user_id:
                        return APIResponse.error(error_code=1006,status_code=status.HTTP_401_UNAUTHORIZED,language=get_language_from_request(request))
                elif user_type == 'member':
                    if notification.type != 'APPMEMBER' or notification.main_id != user_id:
                        return APIResponse.error(error_code=1006,status_code=status.HTTP_401_UNAUTHORIZED,language=get_language_from_request(request))
            else:
                return APIResponse.error(error_code=1006,status_code=status.HTTP_401_UNAUTHORIZED,language=get_language_from_request(request))
        
        # Update notification is_read to True
        notification.is_read = True
        notification.save()
        # Return notification
        serializer = self.get_serializer(notification)
        return APIResponse.success(data=serializer.data, language=get_language_from_request(request))

    @extend_schema(
        summary="Delete notifications by IDs",
        description="Delete multiple notifications by providing a list of notification IDs",
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'notification_ids': {
                        'type': 'array',
                        'items': {'type': 'integer'},
                        'description': 'List of notification IDs to delete'
                    }
                },
                'required': ['notification_ids']
            }
        },
        responses={
            status.HTTP_200_OK: {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'error_code': {'type': 'string', 'nullable': True},
                    'error_message': {'type': 'string', 'nullable': True},
                    'data': {
                        'type': 'object',
                        'properties': {
                            'deleted_count': {'type': 'integer'},
                            'deleted_ids': {
                                'type': 'array',
                                'items': {'type': 'integer'}
                            }
                        }
                    },
                    'api_version': {'type': 'string'}
                }
            },
            status.HTTP_400_BAD_REQUEST: {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'error_code': {'type': 'string'},
                    'error_message': {'type': 'string'},
                    'api_version': {'type': 'string'}
                }
            }
        },
        tags=['Notification']
    )
    @action(detail=False, methods=['post'], url_path='delete')
    def delete_multiple(self, request):
        """
        Delete multiple notifications by providing a list of notification IDs
        """
        try:
            # Get notification IDs from request body
            notification_ids = request.data.get('notification_ids', [])
            notification_service_ids = request.data.get('notification_service_ids', [])
            
            if not notification_ids and not notification_service_ids:
                return APIResponse.error(
                    error_code=2000,
                    status_code=status.HTTP_400_BAD_REQUEST,
                    language=get_language_from_request(request)
                )
            
            if not isinstance(notification_ids, list) and not isinstance(notification_service_ids, list):
                return APIResponse.error(
                    error_code=2000,
                    status_code=status.HTTP_400_BAD_REQUEST,
                    language=get_language_from_request(request)
                )
            
            # Validate that all IDs are integers
            try:
                if notification_ids and len(notification_ids) > 0:
                    notification_ids = [int(id) for id in notification_ids]
                if notification_service_ids and len(notification_service_ids) > 0:
                    notification_service_ids = [int(id) for id in notification_service_ids]
            except (ValueError, TypeError):
                return APIResponse.error(
                    error_code=2000,
                    status_code=status.HTTP_400_BAD_REQUEST,
                    language=get_language_from_request(request)
                )
            
            # Get user information for authorization
            if not request.user or not hasattr(request.user, 'id'):
                return APIResponse.error(
                    error_code=1006,
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    language=get_language_from_request(request)
                )
            
            user_id = request.user.id
            user_type = getattr(request.user, 'user_type', None)
            
            # Initialize variables
            deleted_count = 0
            deleted_count_service = 0
            authorized_ids = []
            authorized_ids_service = []
            
            if notification_ids and len(notification_ids) > 0:
                # Build base queryset for user's notifications
                queryset = TcdAppNotification.objects.filter(id__in=notification_ids)            
                # Apply user-specific filtering
                if user_type == 'consultant':
                    queryset = queryset.filter(type='APPCONSULTANT', main_id=user_id)
                elif user_type == 'member':
                    queryset = queryset.filter(type='APPMEMBER', main_id=user_id)
                else:
                    # For other user types, return error as they shouldn't have notifications
                    return APIResponse.error(
                        error_code=1006,
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        language=get_language_from_request(request)
                    )
                
                # Get the IDs of notifications that the user is authorized to delete
                authorized_ids = list(queryset.values_list('id', flat=True))
                
                if not authorized_ids:
                    return APIResponse.error(
                        error_code=2000,
                        status_code=status.HTTP_400_BAD_REQUEST,
                        language=get_language_from_request(request)
                    )
                
                # Delete the authorized notifications
                deleted_count = queryset.delete()[0]
            
            if notification_service_ids and len(notification_service_ids) > 0:
                queryset_service = TcdNotiConsult.objects.filter(id__in=notification_service_ids)
                if user_type == 'consultant':
                    queryset_service = queryset_service.filter(user_consult_id=user_id)
                else:
                    return APIResponse.error(
                        error_code=1006,
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        language=get_language_from_request(request)
                    )
                    
                authorized_ids_service = list(queryset_service.values_list('id', flat=True))
                deleted_count_service = queryset_service.delete()[0]
            
            # Prepare response data
            response_data = {
                'deleted_count': deleted_count + deleted_count_service,
                'deleted_ids': authorized_ids,
                'deleted_ids_service': authorized_ids_service
            }
            
            return APIResponse.success(
                data=response_data,
                language=get_language_from_request(request)
            )
            
        except Exception as e:
            logger.error(f"Error deleting notifications: {str(e)}")
            return APIResponse.error(
                error_code=5000,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                language=get_language_from_request(request)
            )

    @action(detail=False, methods=['post'], url_path='send')
    def send_notification(self, request):
        """
        Send notification to user from JWT
        """
        try:
            token = request.data.get('token', None) # Optional
            title = request.data.get('title', None)
            body = request.data.get('body', None)
            data = request.data.get('data', {})
            
            # Get user information for authorization
            if not request.user or not hasattr(request.user, 'id'):
                return APIResponse.error(
                    error_code=1006,
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    language=get_language_from_request(request)
                )
            
            # user_id = request.user.id
            user_type = getattr(request.user, 'user_type', None)
            
            if user_type == 'consultant':
                token_app = request.user.token_app
            elif user_type == 'member':
                token_app = request.user.token_app
            else:
                return APIResponse.error(
                    error_code=1006,
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    language=get_language_from_request(request)
                )
            
            # Send notification to user
            send_push_notification(
                token_app if token is None else token,
                title,
                body,
                data
            )
            
            return APIResponse.success(
                data={
                    'message': f'Notification sent successfully'
                },
                language=get_language_from_request(request)
            )
        except Exception as e:
            logger.error(f"Error sending notification: {str(e)}")
            return APIResponse.error(
                error_code=5000,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                language=get_language_from_request(request)
            )

