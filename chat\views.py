# -*- coding: utf-8 -*-
"""
Chat views for API endpoints
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from utils.pagination import CustomPagination
from django.db.models import Q, Max
from django.http import HttpResponse, Http404, FileResponse
from django.conf import settings
import logging
import os
import mimetypes
from datetime import datetime

from .models import TcdChat, TcdChatPrompt, TcdSettingChat
from .serializers import (
    ChatMessageSerializer,
    ChatPromptSerializer,
    ChatSettingSerializer,
    SendMessageSerializer,
    ChatHistorySerializer,
    MobileChatRoomSerializer,
    StartChatSerializer,
    MobileChatSessionSerializer,
    PushNotificationSerializer
)
from authentication.models import TcdUserConsult
from MCDC.models import TcdUsers, TcdCalendar, TcdPersonalGeneralData, TcdCorporateGeneralData, TcdNoProfitGeneralData
from utils.response import APIResponse, get_language_from_request
from drf_spectacular.utils import extend_schema
from .utils import save_uploaded_file, determine_message_type, get_file_url, get_websocket_room_url, get_full_url_for_message
from MCDC.serializers import TcdCalendarSerializer

logger = logging.getLogger(__name__)


def validate_chat_access(user):
    """
    Validate if user has access to chat features
    Only consultants (TcdUserConsult) and staff (TcdUsers) can access chat features
    Members (TcdAppMember) are completely blocked

    Args:
        user: Authenticated user object

    Returns:
        tuple: (is_valid, error_response)
    """
    if not hasattr(user, 'user_type'):
        return False, APIResponse.unauthorized(
            language=getattr(user, 'language', 'th')
        )

    if user.user_type == 'member':
        return False, APIResponse.unauthorized(
            language=getattr(user, 'language', 'th')
        )

    if user.user_type not in ['consultant', 'staff']:
        return False, APIResponse.forbidden(
            language=getattr(user, 'language', 'th')
        )

    return True, None


@extend_schema(
    tags=["Chat"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chat_history(request):
    """
    Get chat history for a specific conversation
    Supports both consultant and staff user types:
    - Consultants: Access their own chat room history automatically
    - Staff: Must provide user_consult_id parameter to specify consultant's room
    """
    try:
        user = request.user

        # Validate chat access - both consultants and staff allowed
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        serializer = ChatHistorySerializer(data=request.GET, context={'request': request})
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=get_language_from_request(request)
            )

        data = serializer.validated_data

        # Determine which consultant's room to access based on user type
        if user.user_type == 'consultant':
            # Consultant accessing their own room
            user_consult_id = user.id
        else:
            # Staff accessing a specific consultant's room
            user_consult_id = data.get('user_consult_id')

            # Validate that the consultant exists
            try:
                TcdUserConsult.objects.get(id=user_consult_id)
            except TcdUserConsult.DoesNotExist:
                return APIResponse.not_found(
                    message="Consultant not found",
                    language=get_language_from_request(request)
                )

        # Build base query for the consultant's room
        queryset = TcdChat.objects.filter(user_consult_id=user_consult_id)

        # Apply additional filters
        if data.get('user_id'):
            # Filter by specific staff member (TcdUsers) if provided
            queryset = queryset.filter(users_id=data['user_id'])

        if data.get('date_from'):
            queryset = queryset.filter(date__gte=data['date_from'])

        if data.get('date_to'):
            queryset = queryset.filter(date__lte=data['date_to'])

        # Order by date ascending for chronological order (oldest first)
        queryset = queryset.order_by('-date')

        # Use CustomPagination for consistent API response format
        paginator = CustomPagination()
        paginator.page_size = data.get('per_page', 20)

        # Get paginated results
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            # Serialize messages with proper timezone handling
            messages = ChatMessageSerializer(
                page,
                many=True,
                context={'request': request}
            ).data
            
            for message in messages:
                subdirectory = getattr(settings, 'CHAT_SUB_DIR', '')
                message['full_url'] = get_full_url_for_message(message['file_src'], subdirectory) if message.get('file_src') else None

            # Return paginated response with proper format
            return paginator.get_paginated_response(messages)

        # Fallback if pagination fails
        messages = ChatMessageSerializer(
            queryset,
            many=True,
            context={'request': request}
        ).data

        return APIResponse.success(
            data=messages,
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in chat_history: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_message(request):
    """
    Send a new chat message
    Only consultants and TcdUsers (staff) can send messages
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        serializer = SendMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=get_language_from_request(request)
            )

        data = serializer.validated_data

        # Determine message routing based on sender type
        if user.user_type == 'consultant':
            # Consultant sending message in their own room
            user_consult_id = user.id
            users_id = data.get('recipient_id')  # TcdUsers (staff) ID (optional)

            # If recipient_id is provided, validate it's a staff member
            if users_id:
                try:
                    TcdUsers.objects.get(id=users_id)
                except TcdUsers.DoesNotExist:
                    return APIResponse.not_found(
                        language=get_language_from_request(request)
                    )
            # If no recipient_id provided, message goes to the room for any staff to see

        else:
            # TcdUsers (staff) responding to consultant's room
            users_id = user.id
            user_consult_id = data.get('recipient_id')  # Consultant ID (required)

            # Validate recipient is a consultant
            if not user_consult_id:
                return APIResponse.validation_error(
                    {'recipient_id': ['Consultant ID is required for staff messages']},
                    language=get_language_from_request(request)
                )

            try:
                TcdUserConsult.objects.get(id=user_consult_id)
            except TcdUserConsult.DoesNotExist:
                return APIResponse.not_found(
                    language=get_language_from_request(request)
                )

        # Handle file upload if present
        uploaded_file = request.FILES.get('file')
        file_name = data.get('file_name')
        file_src = data.get('file_src')
        message_type = data.get('message_type', 'M')
        message_text = data.get('message', '')

        if uploaded_file:
            # Determine file type (image or document)
            file_type = 'image' if uploaded_file.content_type.startswith('image/') else 'document'

            # Save the uploaded file
            save_result = save_uploaded_file(uploaded_file, file_type)
            if not save_result['success']:
                return APIResponse.validation_error(
                    {'file': [save_result['error']]},
                    language=get_language_from_request(request)
                )

            # Override file parameters with actual upload results
            file_name = uploaded_file.name
            file_src = save_result['file_path']

            # Set message type based on file type and clear message text for file uploads
            if file_type == 'image':
                message_type = 'I'
            else:
                message_type = 'D'
            
            # Set message to null when sending files
            message_text = None

        # Set read status based on sender type
        if user.user_type == 'consultant':
            # Consultant sending message - mark as read for consultant
            consult_read = '1'  # Read (consultant sent this message)
            users_read = '0'    # Unread for staff
        else:
            # Staff sending message - mark as read for staff
            consult_read = '0'  # Unread for consultant
            users_read = '1'    # Read (staff sent this message)

        # Create chat message
        chat_message = TcdChat.objects.create(
            type=message_type,
            message=message_text,
            file_name=file_name,
            file_src=file_src,
            user_consult_id=user_consult_id,
            users_id=users_id,
            consult_read=consult_read,
            users_read=users_read,
            date=timezone.now()
        )

        # Serialize the created message
        message_data = ChatMessageSerializer(
            chat_message,
            context={'request': request}
        ).data

        return APIResponse.success(
            data={'message': message_data},
            language=get_language_from_request(request),
            status_code=201
        )

    except Exception as e:
        logger.error(f"Error in send_message: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chat_prompts(request):
    """
    Get available chat prompts/quick replies
    Only consultants and TcdUsers (staff) can access
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        prompts = TcdChatPrompt.objects.filter(status=True).order_by('id')
        serializer = ChatPromptSerializer(
            prompts,
            many=True,
            context={'request': request}
        )

        return APIResponse.success(
            data={'prompts': serializer.data},
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in chat_prompts: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chat_settings(request):
    """
    Get chat settings
    Only consultants and TcdUsers (staff) can access
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response
        settings = TcdSettingChat.objects.filter(status=True).first()
        date_now = timezone.now().date()
        # Check if current date is Saturday or Sunday
        is_weekend = date_now.weekday() >= 5  # 5 = Saturday, 6 = Sunday
        if not is_weekend:
            serializer = ChatSettingSerializer(
                settings,
                context={'request': request}
            )
            # Check if current time is within chat operating hours
            time_now = timezone.now().time()
            start_time = datetime.strptime(serializer.data['start_time'], '%H:%M:%S').time()
            end_time = datetime.strptime(serializer.data['end_time'], '%H:%M:%S').time()            
            if time_now < start_time or time_now > end_time:
                return APIResponse.success(
                    data={'settings': serializer.data},
                    language=get_language_from_request(request)
                )
                
            calendar = TcdCalendar.objects.all()
            # Check if current date matches any holiday
            is_holiday = False
            for i, cal in enumerate(calendar):
                # logger.info(f"calendar_{i} : {cal.calendar_date}")
                if cal.calendar_status == 1:  # Annual holiday - check day and month only
                    if (date_now.day == cal.calendar_date.day and
                            date_now.month == cal.calendar_date.month):
                        is_holiday = True
                        break
                elif cal.calendar_status == 2:  # Non-annual holiday - check exact date
                    # Handle both date and datetime objects
                    cal_date = cal.calendar_date.date() if hasattr(
                        cal.calendar_date, 'date') else cal.calendar_date
                    if date_now == cal_date:
                        is_holiday = True
                        break

            # If it's a holiday, return None settings
            if not is_holiday:
                return APIResponse.success(
                    data={'settings': None},
                    language=get_language_from_request(request)
                )
            else:
                if settings:
                    serializer = ChatSettingSerializer(
                        settings,
                        context={'request': request}
                    )
                    return APIResponse.success(
                        data={'settings': serializer.data},
                        language=get_language_from_request(request)
                    )

        else:
            if settings:
                serializer = ChatSettingSerializer(
                    settings,
                    context={'request': request}
                )
                return APIResponse.success(
                    data={'settings': serializer.data},
                    language=get_language_from_request(request)
                )

    except Exception as e:
        logger.error(f"Error in chat_settings: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_as_read(request):
    """
    Mark messages as read
    Only consultants and TcdUsers (staff) can mark messages as read
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        message_ids = request.data.get('message_ids', [])

        if not message_ids:
            return APIResponse.validation_error(
                {'message_ids': ['This field is required']},
                language=get_language_from_request(request)
            )

        # Update read status based on user type
        if user.user_type == 'consultant':
            # Consultant marking messages as read in their owned rooms
            TcdChat.objects.filter(
                id__in=message_ids,
                user_consult_id=user.id
            ).update(consult_read='1')  # Read status
        else:
            # TcdUsers (staff) marking messages as read
            TcdChat.objects.filter(
                id__in=message_ids,
                users_id=user.id
            ).update(users_read='1')  # Read status

        return APIResponse.success(
            data={'updated_count': len(message_ids)},
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in mark_as_read: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chat_rooms(request):
    """
    Get available chat rooms for the user
    Only consultants can own rooms, TcdUsers (staff) can participate
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        rooms = []

        if user.user_type == 'consultant':
            # Consultant has one room that any staff can join
            consultant_room_exists = TcdChat.objects.filter(
                user_consult_id=user.id
            ).exists()

            if consultant_room_exists:
                # Get all staff members who have participated in this consultant's room
                participating_staff = TcdChat.objects.filter(
                    user_consult_id=user.id
                ).values('users_id').distinct()

                participants = []
                for staff_chat in participating_staff:
                    if staff_chat['users_id']:
                        try:
                            staff_member = TcdUsers.objects.get(id=staff_chat['users_id'])
                            participants.append({
                                'id': staff_member.id,
                                'name': f"{staff_member.firstname} {staff_member.lastname}".strip() or staff_member.username,
                                'type': 'staff',
                                'position': staff_member.position
                            })
                        except TcdUsers.DoesNotExist:
                            continue

                # Get the last message from any staff member
                last_message = TcdChat.objects.filter(
                    user_consult_id=user.id
                ).order_by('-date').first()

                rooms.append({
                    'room_id': f"consultation_{user.id}",
                    'room_name': f"Consultation Room - {getattr(user, 'name', user.username)}",
                    'room_type': 'consultation',
                    'owner': {
                        'id': user.id,
                        'name': getattr(user, 'name', user.username),
                        'type': 'consultant'
                    },
                    'participants': participants,
                    'total_participants': len(participants),
                    'allows_any_staff': True,
                    'last_message': ChatMessageSerializer(last_message).data if last_message else None,
                    'unread_count': TcdChat.objects.filter(
                        user_consult_id=user.id,
                        consult_read='0'  # Unread status
                    ).count()
                })
            else:
                # Consultant has no room yet - show empty room that can be created
                rooms.append({
                    'room_id': f"consultation_{user.id}",
                    'room_name': f"Consultation Room - {getattr(user, 'name', user.username)}",
                    'room_type': 'consultation',
                    'owner': {
                        'id': user.id,
                        'name': getattr(user, 'name', user.username),
                        'type': 'consultant'
                    },
                    'participants': [],
                    'total_participants': 0,
                    'allows_any_staff': True,
                    'last_message': None,
                    'unread_count': 0
                })
        else:
            # TcdUsers (staff) - get consultant rooms they participate in
            consultant_chats = TcdChat.objects.filter(
                users_id=user.id
            ).values('user_consult_id').distinct()

            for chat in consultant_chats:
                if chat['user_consult_id']:
                    try:
                        consultant = TcdUserConsult.objects.get(
                            id=chat['user_consult_id'])

                        # Get all staff members who have participated in this consultant's room
                        participating_staff = TcdChat.objects.filter(
                            user_consult_id=chat['user_consult_id']
                        ).values('users_id').distinct()

                        participants = []
                        for staff_chat in participating_staff:
                            if staff_chat['users_id']:
                                try:
                                    staff_member = TcdUsers.objects.get(id=staff_chat['users_id'])
                                    participants.append({
                                        'id': staff_member.id,
                                        'name': f"{staff_member.firstname} {staff_member.lastname}".strip() or staff_member.username,
                                        'type': 'staff',
                                        'position': staff_member.position
                                    })
                                except TcdUsers.DoesNotExist:
                                    continue

                        # Get the last message from the entire room
                        last_message = TcdChat.objects.filter(
                            user_consult_id=chat['user_consult_id']
                        ).order_by('-date').first()

                        rooms.append({
                            'room_id': f"consultation_{chat['user_consult_id']}",
                            'room_name': f"Chat with {getattr(consultant, 'name', consultant.username)}",
                            'room_type': 'consultation',
                            'owner': {
                                'id': consultant.id,
                                'name': getattr(consultant, 'name', consultant.username),
                                'type': 'consultant'
                            },
                            'participants': participants,
                            'total_participants': len(participants),
                            'allows_any_staff': True,
                            'last_message': ChatMessageSerializer(last_message).data if last_message else None,
                            'unread_count': TcdChat.objects.filter(
                                users_id=user.id,
                                user_consult_id=chat['user_consult_id'],
                                users_read='0'  # Unread status
                            ).count()
                        })
                    except TcdUserConsult.DoesNotExist:
                        continue

        return APIResponse.success(
            data={'rooms': rooms},
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in chat_rooms: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_chat(request):
    """
    Start a new chat session for mobile application using existing TcdChat model
    Only consultants can create/own chat rooms, TcdUsers (staff) can join
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        serializer = StartChatSerializer(
            data=request.data, context={'request': request})
        if not serializer.is_valid():
            return APIResponse.validation_error(
                serializer.errors,
                language=get_language_from_request(request)
            )

        data = serializer.validated_data

        # Determine chat participants based on new access control
        room_id = None
        room_name = ""
        consultant_id = None

        if user.user_type == 'consultant':
            # Consultant creating/owning a room (no staff_id required)
            consultant_id = user.id
            room_id = f"consultation_{consultant_id}"
            room_name = f"Consultation Room - {getattr(user, 'name', user.username)}"

        else:
            # TcdUsers (staff) joining consultant's room
            consultant_id = data.get('consultant_id')
            room_id = f"consultation_{consultant_id}"

            # Get consultant info and validate existence
            try:
                consultant = TcdUserConsult.objects.get(id=consultant_id)
                room_name = f"Chat with {getattr(consultant, 'name', consultant.username)}"
            except TcdUserConsult.DoesNotExist:
                return APIResponse.not_found(
                    language=get_language_from_request(request)
                )

        # Check if chat history exists for this consultant (any staff interactions)
        existing_chat = TcdChat.objects.filter(
            user_consult_id=consultant_id
        ).first()

        created = existing_chat is None

        # Get participating staff members for this consultant's room
        participating_staff = TcdChat.objects.filter(
            user_consult_id=consultant_id
        ).values('users_id').distinct()

        participant_info = []
        for staff_chat in participating_staff:
            if staff_chat['users_id']:
                try:
                    staff_member = TcdUsers.objects.get(id=staff_chat['users_id'])
                    participant_info.append({
                        'id': staff_member.id,
                        'name': f"{staff_member.firstname} {staff_member.lastname}".strip() or staff_member.username,
                        'type': 'staff',
                        'position': staff_member.position
                    })
                except TcdUsers.DoesNotExist:
                    continue

        # Create room data structure
        room_data = {
            'room_id': room_id,
            'room_name': room_name,
            'room_type': data.get('chat_type', 'consultation'),
            'owner': {
                'id': consultant_id,
                'name': getattr(consultant, 'name', consultant.username) if 'consultant' in locals() else getattr(user, 'name', user.username),
                'type': 'consultant'
            },
            'participant_info': participant_info,
            'last_message': None,
            'unread_count': 0,
            'created_at': timezone.now().isoformat(),
            'total_participants': len(participant_info),
            'allows_any_staff': True  # Indicates any staff can join and respond
        }

        # Get last message if exists
        if existing_chat:
            last_message = TcdChat.objects.filter(
                user_consult_id=consultant_id
            ).order_by('-date').first()

            if last_message:
                # Determine sender type and info
                sender_type = 'staff' if last_message.users else 'consultant'
                sender_name = 'Consultant'

                if sender_type == 'staff' and last_message.users:
                    try:
                        sender_staff = TcdUsers.objects.get(id=last_message.users.id)
                        sender_name = f"{sender_staff.firstname} {sender_staff.lastname}".strip() or sender_staff.username
                    except TcdUsers.DoesNotExist:
                        sender_name = 'Staff Member'
                elif sender_type == 'consultant':
                    try:
                        sender_consultant = TcdUserConsult.objects.get(id=consultant_id)
                        sender_name = getattr(sender_consultant, 'name', sender_consultant.username)
                    except TcdUserConsult.DoesNotExist:
                        sender_name = 'Consultant'

                room_data['last_message'] = {
                    'id': last_message.id,
                    'message': last_message.message,
                    'file_name': last_message.file_name,
                    'date': last_message.date.isoformat() if last_message.date else None,
                    'sender_type': sender_type,
                    'sender_name': sender_name
                }

            # Get unread count for the requesting user
            if user.user_type == 'consultant':
                room_data['unread_count'] = TcdChat.objects.filter(
                    user_consult_id=consultant_id,
                    consult_read='0'  # Unread status
                ).count()
            else:
                # For staff, count unread messages they haven't read
                room_data['unread_count'] = TcdChat.objects.filter(
                    user_consult_id=consultant_id,
                    users_id=user.id,
                    users_read='0'  # Unread status
                ).count()

        session_id = f"{room_id}_{user.id}_{user.user_type}"

        # Generate WebSocket URL with token parameter for authentication
        # Extract token from request headers
        auth_header = request.headers.get('Authorization', '')
        token = ''
        if auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')

        # Build WebSocket URL using configurable utility function
        websocket_url = get_websocket_room_url(room_id)
        # Note: Token authentication is handled via query parameter in production
        # For now, we don't include token in URL for security reasons

        response_data = {
            'room': room_data,
            'session_id': session_id,
            'websocket_url': websocket_url,
            'created': created,
            'security_info': {
                'authentication_required': True,
                'token_included': bool(token),
                'user_type': user.user_type,
                'access_level': 'consultant_room' if user.user_type == 'consultant' else 'staff_participant'
            }
        }

        return APIResponse.success(
            data=response_data,
            language=get_language_from_request(request),
            status_code=201 if created else 200
        )

    except Exception as e:
        logger.error(f"Error in start_chat: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mobile_chat_rooms(request):
    """
    Get chat rooms for mobile application using existing TcdChat model
    Only consultants and TcdUsers (staff) can access
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        rooms = []

        if user.user_type == 'consultant':
            # Consultant has one room that any staff can join
            # Get all staff members who have participated in this consultant's room
            participating_staff = TcdChat.objects.filter(
                user_consult_id=user.id
            ).values('users_id').distinct()

            participant_info = []
            for staff_chat in participating_staff:
                if staff_chat['users_id']:
                    try:
                        staff_member = TcdUsers.objects.get(id=staff_chat['users_id'])
                        participant_info.append({
                            'id': staff_member.id,
                            'name': f"{staff_member.firstname} {staff_member.lastname}".strip() or staff_member.username,
                            'type': 'staff',
                            'position': staff_member.position
                        })
                    except TcdUsers.DoesNotExist:
                        continue

            # Get the last message from any staff member
            last_message = TcdChat.objects.filter(
                user_consult_id=user.id
            ).order_by('-date').first()

            unread_count = TcdChat.objects.filter(
                user_consult_id=user.id,
                consult_read='0'  # Unread status
            ).count()

            room_data = {
                'room_id': f"consultation_{user.id}",
                'room_name': f"Consultation Room - {getattr(user, 'name', user.username)}",
                'room_type': 'consultation',
                'owner': {
                    'id': user.id,
                    'name': getattr(user, 'name', user.username),
                    'type': 'consultant'
                },
                'participant_info': participant_info,
                'total_participants': len(participant_info),
                'allows_any_staff': True,
                'last_message': {
                    'id': last_message.id,
                    'message': last_message.message,
                    'file_name': last_message.file_name,
                    'date': last_message.date.isoformat() if last_message.date else None,
                    'sender_type': 'staff' if last_message.users else 'consultant'
                } if last_message else None,
                'unread_count': unread_count,
                'created_at': last_message.date.isoformat() if last_message else timezone.now().isoformat()
            }
            rooms.append(room_data)

        else:
            # TcdUsers (staff) - get consultant rooms they participate in
            consultant_chats = TcdChat.objects.filter(
                users_id=user.id
            ).values('user_consult_id').distinct()

            for chat in consultant_chats:
                if chat['user_consult_id']:
                    try:
                        consultant = TcdUserConsult.objects.get(id=chat['user_consult_id'])

                        # Get all staff members who have participated in this consultant's room
                        participating_staff = TcdChat.objects.filter(
                            user_consult_id=chat['user_consult_id']
                        ).values('users_id').distinct()

                        participant_info = []
                        for staff_chat in participating_staff:
                            if staff_chat['users_id']:
                                try:
                                    staff_member = TcdUsers.objects.get(id=staff_chat['users_id'])
                                    participant_info.append({
                                        'id': staff_member.id,
                                        'name': f"{staff_member.firstname} {staff_member.lastname}".strip() or staff_member.username,
                                        'type': 'staff',
                                        'position': staff_member.position
                                    })
                                except TcdUsers.DoesNotExist:
                                    continue

                        # Get the last message from the entire room
                        last_message = TcdChat.objects.filter(
                            user_consult_id=chat['user_consult_id']
                        ).order_by('-date').first()

                        unread_count = TcdChat.objects.filter(
                            users_id=user.id,
                            user_consult_id=chat['user_consult_id'],
                            users_read='0'  # Unread status
                        ).count()

                        room_data = {
                            'room_id': f"consultation_{chat['user_consult_id']}",
                            'room_name': f"Chat with {getattr(consultant, 'name', consultant.username)}",
                            'room_type': 'consultation',
                            'owner': {
                                'id': consultant.id,
                                'name': getattr(consultant, 'name', consultant.username),
                                'type': 'consultant'
                            },
                            'participant_info': participant_info,
                            'total_participants': len(participant_info),
                            'allows_any_staff': True,
                            'last_message': {
                                'id': last_message.id,
                                'message': last_message.message,
                                'file_name': last_message.file_name,
                                'date': last_message.date.isoformat() if last_message.date else None,
                                'sender_type': 'staff' if last_message.users else 'consultant'
                            } if last_message else None,
                            'unread_count': unread_count,
                            'created_at': last_message.date.isoformat() if last_message else timezone.now().isoformat()
                        }
                        rooms.append(room_data)
                    except TcdUserConsult.DoesNotExist:
                        continue

        # Sort rooms by last message date
        rooms.sort(key=lambda x: x['created_at'], reverse=True)

        return APIResponse.success(
            data={'rooms': rooms},
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in mobile_chat_rooms: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_online_status(request):
    """
    Update user online status (simplified for existing models)
    Only consultants and TcdUsers (staff) can update status
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        session_id = request.data.get('session_id')
        is_online = request.data.get('is_online', True)

        if not session_id:
            return APIResponse.validation_error(
                {'session_id': ['This field is required']},
                language=get_language_from_request(request)
            )

        # For now, just return success since we don't have session tracking table
        # In a real implementation, you might want to use cache or another mechanism
        return APIResponse.success(
            data={
                'status': 'updated',
                'session_id': session_id,
                'is_online': is_online,
                'user_type': user.user_type,
                'message': 'Online status updated (using existing models)'
            },
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in update_online_status: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"],
    summary="Download chat file",
    description="Download uploaded files from chat messages with authentication and permission checks"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def download_chat_file(request, message_id):
    """
    Download uploaded files from chat messages with proper authentication and permission checks

    Args:
        request: HTTP request object
        message_id: ID of the chat message containing the file

    Returns:
        FileResponse: File download response or error response
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        # Get the chat message
        try:
            chat_message = TcdChat.objects.get(id=message_id)
        except TcdChat.DoesNotExist:
            return APIResponse.not_found(
                language=get_language_from_request(request)
            )

        # Check if message has a file
        if not chat_message.file_src or not chat_message.file_name:
            return APIResponse.validation_error(
                {'message': ['This message does not contain a file']},
                language=get_language_from_request(request)
            )

        # Check user permissions to access this file
        has_access = False

        if user.user_type == 'consultant':
            # Consultant can access files from their own room
            has_access = (chat_message.user_consult_id == user.id)
        else:
            # Staff can access files from rooms they participate in
            # Check if staff has sent/received messages in this conversation
            staff_participation = TcdChat.objects.filter(
                user_consult_id=chat_message.user_consult_id,
                users_id=user.id
            ).exists()
            has_access = staff_participation

        if not has_access:
            return APIResponse.unauthorized(
                language=get_language_from_request(request)
            )

        # Build file path
        upload_dir = getattr(settings, 'UPLOAD_DIR', 'uploads')
        file_path = os.path.join(upload_dir, chat_message.file_src)

        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return APIResponse.not_found(
                message="File not found on server",
                language=get_language_from_request(request)
            )

        # Determine content type
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            if chat_message.type == 'I':
                content_type = 'image/jpeg'  # Default for images
            else:
                content_type = 'application/octet-stream'  # Default for documents

        # Create file response
        response = FileResponse(
            open(file_path, 'rb'),
            content_type=content_type,
            filename=chat_message.file_name
        )

        # Set appropriate headers
        if chat_message.type == 'I':
            # For images, allow inline display
            response['Content-Disposition'] = f'inline; filename="{chat_message.file_name}"'
        else:
            # For documents, force download
            response['Content-Disposition'] = f'attachment; filename="{chat_message.file_name}"'

        logger.info(f"File downloaded: {chat_message.file_name} by user {user.id}")
        return response

    except Exception as e:
        logger.error(f"Error in download_chat_file: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"],
    summary="Get file metadata",
    description="Get metadata information about uploaded files in chat messages"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_file_metadata(request, message_id):
    """
    Get metadata information about uploaded files in chat messages

    Args:
        request: HTTP request object
        message_id: ID of the chat message containing the file

    Returns:
        APIResponse: File metadata or error response
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        # Get the chat message
        try:
            chat_message = TcdChat.objects.get(id=message_id)
        except TcdChat.DoesNotExist:
            return APIResponse.not_found(
                language=get_language_from_request(request)
            )

        # Check if message has a file
        if not chat_message.file_src or not chat_message.file_name:
            return APIResponse.validation_error(
                {'message': ['This message does not contain a file']},
                language=get_language_from_request(request)
            )

        # Check user permissions to access this file
        has_access = False

        if user.user_type == 'consultant':
            # Consultant can access files from their own room
            has_access = (chat_message.user_consult_id == user.id)
        else:
            # Staff can access files from rooms they participate in
            staff_participation = TcdChat.objects.filter(
                user_consult_id=chat_message.user_consult_id,
                users_id=user.id
            ).exists()
            has_access = staff_participation

        if not has_access:
            return APIResponse.unauthorized(
                language=get_language_from_request(request)
            )

        # Build file path and get file info
        upload_dir = getattr(settings, 'UPLOAD_DIR', 'uploads')
        file_path = os.path.join(upload_dir, chat_message.file_src)

        file_exists = os.path.exists(file_path)
        file_size = os.path.getsize(file_path) if file_exists else 0

        # Determine content type
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            if chat_message.type == 'I':
                content_type = 'image/jpeg'
            else:
                content_type = 'application/octet-stream'

        # Build metadata response
        metadata = {
            'message_id': chat_message.id,
            'file_name': chat_message.file_name,
            'file_src': chat_message.file_src,
            'file_type': 'image' if chat_message.type == 'I' else 'document',
            'message_type': chat_message.type,
            'content_type': content_type,
            'file_size': file_size,
            'file_exists': file_exists,
            'download_url': f"/api/chat/file/download/{chat_message.id}/",
            'full_url': get_file_url(chat_message.file_src) if file_exists else None,
            'uploaded_date': chat_message.date.isoformat() if chat_message.date else None
        }

        return APIResponse.success(
            data={'file_metadata': metadata},
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in get_file_metadata: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_all_as_read(request):
    """
    Mark all unread chat messages as read for the authenticated user

    For consultants: Marks all unread messages in their own room as read (consult_read = '1')
    For staff: Marks all unread messages as read for a specific consultant's room (users_read = '1')

    Request body (for staff only):
        - user_consult_id: ID of the consultant whose messages to mark as read (required for staff)

    Returns:
        APIResponse with count of messages marked as read
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        updated_count = 0

        if user.user_type == 'consultant':
            # Consultant marking all messages in their own room as read
            updated_count = TcdChat.objects.filter(
                user_consult_id=user.id,
                consult_read='0'  # Only update unread messages
            ).update(consult_read='1')  # Mark as read

        else:
            # Staff user - requires user_consult_id parameter
            user_consult_id = request.data.get('user_consult_id')

            if not user_consult_id:
                return APIResponse.validation_error(
                    {'user_consult_id': ['This field is required for staff users']},
                    language=get_language_from_request(request)
                )

            # Validate that the consultant exists
            try:
                TcdUserConsult.objects.get(id=user_consult_id)
            except TcdUserConsult.DoesNotExist:
                return APIResponse.not_found(
                    message="Consultant not found",
                    language=get_language_from_request(request)
                )

            # Check if staff has access to this consultant's room
            # (Staff must have participated in the conversation)
            staff_participation = TcdChat.objects.filter(
                user_consult_id=user_consult_id,
                users_id=user.id
            ).exists()

            if not staff_participation:
                return APIResponse.unauthorized(
                    message="You don't have access to this consultant's chat room",
                    language=get_language_from_request(request)
                )

            # Mark all unread messages in this consultant's room as read for this staff member
            updated_count = TcdChat.objects.filter(
                user_consult_id=user_consult_id,
                users_read='0'  # Only update unread messages
            ).update(users_read='1')  # Mark as read

        return APIResponse.success(
            data={
                'updated_count': updated_count,
                'user_type': user.user_type,
                'message': f'Marked {updated_count} messages as read'
            },
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in mark_all_as_read: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"],
    summary="List chat files",
    description="Get list of files from chat messages with proper access control and filtering"
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_chat_files(request):
    """
    Get list of files from chat messages with proper access control

    Query Parameters:
        - consult_id: Filter by consultation ID (optional)
        - file_type: Filter by file type ('I' for images, 'D' for documents, optional)
        - page: Page number (default: 1)
        - per_page: Items per page (default: 20, max: 100)

    Returns:
        APIResponse: List of accessible files with metadata
    """
    try:
        user = request.user

        # Validate chat access - block members completely
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        # Get query parameters
        consult_id = request.GET.get('consult_id')
        file_type = request.GET.get('file_type')
        page = int(request.GET.get('page', 1))
        per_page = min(int(request.GET.get('per_page', 20)), 100)

        # Validate file_type if provided
        if file_type and file_type not in ['I', 'D']:
            return APIResponse.validation_error(
                {'file_type': ['Must be "I" for images or "D" for documents']},
                language=get_language_from_request(request)
            )

        # Validate consult_id if provided
        if consult_id:
            try:
                consult_id = int(consult_id)
                # Verify consultant exists
                TcdUserConsult.objects.get(id=consult_id)
            except (ValueError, TcdUserConsult.DoesNotExist):
                return APIResponse.validation_error(
                    {'consult_id': ['Invalid consultant ID']},
                    language=get_language_from_request(request)
                )

        # Calculate offset
        offset = (page - 1) * per_page

        # Get files using service
        from .services import ChatService
        result = ChatService.get_chat_files(
            user_id=user.id,
            user_type=user.user_type,
            consult_id=consult_id,
            file_type=file_type,
            limit=per_page,
            offset=offset
        )

        if not result['success']:
            return APIResponse.server_error(
                message=result.get('error', 'Failed to retrieve files'),
                language=get_language_from_request(request)
            )

        # Build pagination info
        pagination_info = {
            'page': page,
            'per_page': per_page,
            'total_items': result['total_count'],
            'has_next': result['has_more'],
            'has_previous': page > 1
        }

        response_data = {
            'files': result['files'],
            'pagination': pagination_info,
            'filters': {
                'consult_id': consult_id,
                'file_type': file_type,
                'file_type_description': {
                    'I': 'Images only',
                    'D': 'Documents only',
                    None: 'All file types'
                }.get(file_type)
            }
        }

        return APIResponse.success(
            data=response_data,
            language=get_language_from_request(request)
        )

    except Exception as e:
        logger.error(f"Error in list_chat_files: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def answer_prompt(request, prompt_id):
    try:
        # Validate chat access
        user = request.user
        is_valid, error_response = validate_chat_access(user)
        if not is_valid:
            return error_response

        # Get the prompt object
        prompt_obj = TcdChatPrompt.objects.get(id=prompt_id)

        # Get language from request header
        language = get_language_from_request(request)

        # Get the appropriate answer based on language
        if language == 'en':
            message = prompt_obj.answer_en
        else:
            message = prompt_obj.answer_th

        # Get user_consult from JWT
        user_consult_id = None
        if user.user_type == 'consultant':
            user_consult_id = user.id
        elif user.user_type == 'staff':
            # For staff, they need to specify which consultant's room they're responding in
            # This could be passed as a parameter or derived from context
            user_consult_id = request.data.get('user_consult_id')
            if not user_consult_id:
                return APIResponse.validation_error(
                    {'user_consult_id': ['This field is required for staff users']},
                    language=language
                )

        # Initialize TcdChat object with prompt answer data
        chat_obj = TcdChat()
        chat_obj.type = 'M'  # Message conversation type
        chat_obj.message = message
        chat_obj.user_consult_id = user_consult_id
        chat_obj.users_id = 1  # Default staff user ID
        chat_obj.consult_read = '1'  # Mark as read
        chat_obj.users_read = '1'  # Mark as read
        chat_obj.date = timezone.now()

        # Save the chat message
        chat_obj.save()

        # Serialize the created message
        message_data = ChatMessageSerializer(
            chat_obj,
            context={'request': request}
        ).data

        return APIResponse.success(
            data={'message': message_data},
            language=language,
            status_code=201
        )

    except TcdChatPrompt.DoesNotExist:
        return APIResponse.not_found(
            language=get_language_from_request(request)
        )
    except Exception as e:
        logger.error(f"Error in answer_prompt: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )


@extend_schema(
    tags=["Chat"]
)
@api_view(['get'])
@permission_classes([IsAuthenticated])
def get_chat_check_status(request):
    try:
        lang = get_language_from_request(request)
        user = request.user
        count_unread = 0
        can_chat = True
        if user.user_type == 'consultant':
            # count unread messages for consultant
            count_unread = TcdChat.objects.filter(
                user_consult_id=user.id,
                consult_read='0'
            ).count()
            
            # check status for use chat feature
            tcd_setting_chat = TcdSettingChat.objects.get(id=1)
            if tcd_setting_chat.is_message_limited:
                user_consult = TcdUserConsult.objects.get(id=user.id)
                # Check if consultant is in service request process
                if user_consult.consult_type == 1:
                    # Personal consultant - check personal_general_data table
                    try:
                        personal_data = TcdPersonalGeneralData.objects.get(user_consult_id=user.id)
                        if personal_data.flow_state not in ['4', '0']:
                            can_chat = False
                    except TcdPersonalGeneralData.DoesNotExist:
                        pass
                elif user_consult.consult_type == 2:
                    # Corporate consultant - check based on corporate_type_id
                    if user_consult.corporate_type_id == 1:
                        # Corporate type 1 - check corporate_general_data table
                        try:
                            corporate_data = TcdCorporateGeneralData.objects.get(user_consult_id=user.id)
                            if corporate_data.flow_state not in ['4', '0']:
                                can_chat = False
                        except TcdCorporateGeneralData.DoesNotExist:
                            pass
                    else:
                        # Corporate type != 1 - check no_profit_general_data table
                        try:
                            no_profit_data = TcdNoProfitGeneralData.objects.get(user_consult_id=user.id)
                            if no_profit_data.flow_state not in ['4', '0']:
                                can_chat = False
                        except TcdNoProfitGeneralData.DoesNotExist:
                            pass
        else:
            return APIResponse.unauthorized(
                language=lang
            )
            
        return APIResponse.success(
            data={
                'can_chat': can_chat,
                'count_unread': count_unread,
            },
            language=lang
        )
    except Exception as e:
        logger.error(f"Error in get_chat_stat: {str(e)}")
        return APIResponse.server_error(
            language=lang
        )

