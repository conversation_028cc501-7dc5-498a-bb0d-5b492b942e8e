# -*- coding: utf-8 -*-
"""
Custom exception handlers for REST framework
"""
import logging
from rest_framework.views import exception_handler
from rest_framework import status
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from .response import APIResponse, get_language_from_request

logger = logging.getLogger(__name__)

def custom_exception_handler(exc, context):
    """
    Custom exception handler for REST framework
    
    Args:
        exc: Exception
        context: Exception context
        
    Returns:
        Response: APIResponse
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # Get request from context
    request = context.get('request')
    language = get_language_from_request(request) if request else 'th'
    
    # Get detailed log of exception
    logger.error(f"Exception occurred: {exc.__class__.__name__} - {str(exc)}")
    
    # Handle JWT authentication errors
    if isinstance(exc, (TokenError, InvalidToken)):
        logger.error(f"JWT Token error: {str(exc)}")
        return APIResponse.error(
            error_code=1005,  # Invalid token
            language=language,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    # If response is already handled by DRF, just log and return
    if response is not None:
        return response
    
    # Return default server error for unhandled exceptions
    logger.error(f"Unhandled exception: {exc.__class__.__name__} - {str(exc)}")
    return APIResponse.error(
        error_code=5000,  # System error
        language=language,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
    ) 