#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Generate RSA keys for JWT authentication
"""

import os
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa

def generate_rsa_keys():
    """Generate RSA private and public keys for JWT"""
    
    # Create keys directory if it doesn't exist
    keys_dir = os.path.join(os.path.dirname(__file__), 'keys')
    os.makedirs(keys_dir, exist_ok=True)
    
    private_key_path = os.path.join(keys_dir, 'private_key.pem')
    public_key_path = os.path.join(keys_dir, 'public_key.pem')
    
    # Check if keys already exist
    if os.path.exists(private_key_path) and os.path.exists(public_key_path):
        print("🔑 JWT keys already exist:")
        print(f"   Private key: {private_key_path}")
        print(f"   Public key: {public_key_path}")
        return
    
    print("🔐 Generating RSA keys for JWT authentication...")
    
    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # Get public key
    public_key = private_key.public_key()
    
    # Serialize private key
    private_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    
    # Serialize public key
    public_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    
    # Write private key
    with open(private_key_path, 'wb') as f:
        f.write(private_pem)
    
    # Write public key
    with open(public_key_path, 'wb') as f:
        f.write(public_pem)
    
    print("✅ RSA keys generated successfully:")
    print(f"   Private key: {private_key_path}")
    print(f"   Public key: {public_key_path}")
    print("\n🔄 Please restart the Django server to use the new keys.")

if __name__ == "__main__":
    try:
        generate_rsa_keys()
    except Exception as e:
        print(f"❌ Error generating keys: {e}")
        print("💡 Make sure you have the 'cryptography' package installed:")
        print("   pip install cryptography")
