# -*- coding: utf-8 -*-
"""
Project Owner Notification Service
"""
import logging
from django.utils import timezone
from django.db import transaction
from typing import Optional, Dict

from authentication.models import TcdAppMember
from project.models import TcdAppProject, TcdAppProjectConsult
from .models import TcdAppNotification
from utils.response import service_success_response, service_error_response
from utils.firebase_notification import send_project_matching_notification

logger = logging.getLogger(__name__)


class ProjectOwnerNotificationService:
    """
    Service สำหรับแจ้งเตือนสมาชิกเจ้าของโครงการ
    เมื่อมีที่ปรึกษาแสดงความสนใจในโครงการ
    """
    
    @staticmethod
    def notify_project_owner(app_project_id: int, user_consult_id: int, language: str = 'th') -> Dict:
        """
        แจ้งเตือนสมาชิกเจ้าของโครงการเมื่อมีที่ปรึกษาสนใจ
        
        Args:
            app_project_id (int): ID ของโครงการ
            user_consult_id (int): ID ของที่ปรึกษาที่สนใจ
            language (str): ภาษา
            
        Returns:
            dict: ผลลัพธ์การแจ้งเตือน
        """
        try:
            logger.info(f"Starting notification process for project {app_project_id}, consultant {user_consult_id}")
            
            # 1. ดึงข้อมูลสมาชิกเจ้าของโครงการ
            project_owner_data = ProjectOwnerNotificationService._get_project_owner_data(
                app_project_id, user_consult_id
            )
            
            if not project_owner_data['success']:
                return project_owner_data
            
            owner_info = project_owner_data['data']
            app_member = owner_info['app_member']
            project = owner_info['project']
            
            # 2. สร้างการแจ้งเตือนในฐานข้อมูล
            notification_result = ProjectOwnerNotificationService._create_notification_record(
                app_member=app_member,
                project=project,
                language=language
            )
            
            if not notification_result['success']:
                return notification_result
            
            notification = notification_result['data']
            
            # 3. ส่ง Push Notification (ถ้ามีเงื่อนไข)
            push_result = ProjectOwnerNotificationService._send_push_notification(
                app_member=app_member,
                project=project,
                notification=notification
            )
            
            logger.info(f"Notification process completed for project {app_project_id}")
            
            return service_success_response({
                'notification_id': notification.id,
                'app_member_id': app_member.id,
                'project_id': project.id,
                'project_name': project.name,
                'member_email': app_member.email,
                'push_notification_sent': push_result['sent'],
                'push_notification_result': push_result.get('result', {}),
                'message': 'Project owner notification sent successfully'
            }, language)
            
        except Exception as e:
            logger.error(f"Error in notify_project_owner: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return service_error_response(5000, language)  # System error
    
    @staticmethod
    def _get_project_owner_data(app_project_id: int, user_consult_id: int) -> Dict:
        """
        ดึงข้อมูลสมาชิกเจ้าของโครงการ
        
        Args:
            app_project_id (int): ID ของโครงการ
            user_consult_id (int): ID ของที่ปรึกษา
            
        Returns:
            dict: ข้อมูลเจ้าของโครงการ
        """
        try:
            # ดึงข้อมูล project consult เพื่อหา app_member_id
            project_consult = TcdAppProjectConsult.objects.filter(
                app_project_id=app_project_id,
                user_consult_id=user_consult_id
            ).first()
            
            if not project_consult:
                logger.error(f"Project consult not found for project {app_project_id}, consultant {user_consult_id}")
                return service_error_response(3002, 'th')  # Data not found
            
            # ดึงข้อมูลสมาชิกเจ้าของโครงการ
            app_member = TcdAppMember.objects.filter(
                id=int(project_consult.app_member_id)
            ).first()
            
            if not app_member:
                logger.error(f"App member not found with ID {project_consult.app_member_id}")
                return service_error_response(3002, 'th')  # Data not found
            
            # ดึงข้อมูลโครงการ
            project = TcdAppProject.objects.filter(id=app_project_id).first()
            
            if not project:
                logger.error(f"Project not found with ID {app_project_id}")
                return service_error_response(3002, 'th')  # Data not found
            
            logger.info(f"Found project owner: {app_member.email} for project: {project.name}")
            
            return service_success_response({
                'app_member': app_member,
                'project': project,
                'project_consult': project_consult
            }, 'th')
            
        except Exception as e:
            logger.error(f"Error getting project owner data: {str(e)}")
            return service_error_response(5000, 'th')  # System error
    
    @staticmethod
    def _create_notification_record(app_member: TcdAppMember, project: TcdAppProject, language: str = 'th') -> Dict:
        """
        สร้างระเบียนการแจ้งเตือนในฐานข้อมูล
        
        Args:
            app_member: สมาชิกเจ้าของโครงการ
            project: โครงการ
            language: ภาษา
            
        Returns:
            dict: ผลลัพธ์การสร้างระเบียน
        """
        try:
            with transaction.atomic():
                notification = TcdAppNotification.objects.create(
                    noti_type="matching",
                    type="APPMEMBER",
                    main_id=app_member.id,
                    ref_id=project.id,
                    header="ศูนย์ข้อมูลที่ปรึกษา",
                    detail="ท่านมีที่ปรึกษาที่สนใจในโครงการที่ประกาศ",
                    create_date=timezone.now(),
                    is_read=False
                )
                
                logger.info(f"Created notification record ID: {notification.id}")
                
                return service_success_response({
                    'notification': notification
                }, language)
                
        except Exception as e:
            logger.error(f"Error creating notification record: {str(e)}")
            return service_error_response(3005, language)  # Cannot save data
    
    @staticmethod
    def _send_push_notification(app_member: TcdAppMember, project: TcdAppProject, notification: TcdAppNotification) -> Dict:
        """
        ส่ง Push Notification ให้สมาชิกเจ้าของโครงการ
        
        Args:
            app_member: สมาชิกเจ้าของโครงการ
            project: โครงการ
            notification: ระเบียนการแจ้งเตือน
            
        Returns:
            dict: ผลลัพธ์การส่ง push notification
        """
        try:
            # ตรวจสอบเงื่อนไขการส่ง push notification
            if not app_member.token_app or app_member.token_app.strip() == "":
                logger.info(f"No device token for member {app_member.id}")
                return {'sent': False, 'reason': 'No device token'}
            
            if app_member.is_notification != "1":
                logger.info(f"Push notification disabled for member {app_member.id}")
                return {'sent': False, 'reason': 'Push notification disabled'}
            
            # ส่ง push notification ผ่าน Firebase
            push_result = send_project_matching_notification(
                device_token=app_member.token_app,
                project_name=project.name,
                project_id=project.id
            )
            
            if push_result['success']:
                logger.info(f"Push notification sent successfully to member {app_member.id}")
                return {
                    'sent': True,
                    'result': push_result
                }
            else:
                logger.error(f"Failed to send push notification: {push_result.get('error', 'Unknown error')}")
                return {
                    'sent': False,
                    'reason': 'Firebase send failed',
                    'result': push_result
                }
                
        except Exception as e:
            logger.error(f"Error sending push notification: {str(e)}")
            return {
                'sent': False,
                'reason': f'Exception: {str(e)}'
            }
    
    @staticmethod
    def get_member_notifications(app_member_id: int, page: int = 1, page_size: int = 20, language: str = 'th') -> Dict:
        """
        ดึงรายการการแจ้งเตือนของสมาชิก
        
        Args:
            app_member_id (int): ID ของสมาชิก
            page (int): หน้าที่ต้องการ
            page_size (int): จำนวนรายการต่อหน้า
            language (str): ภาษา
            
        Returns:
            dict: รายการการแจ้งเตือน
        """
        try:
            # ดึงการแจ้งเตือนของสมาชิก
            notifications = TcdAppNotification.objects.filter(
                type="APPMEMBER",
                main_id=app_member_id
            ).order_by('-create_date')
            
            # Pagination
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_notifications = notifications[start_index:end_index]
            
            # แปลงเป็น dict
            notification_list = []
            for notification in paginated_notifications:
                notification_data = {
                    'id': notification.id,
                    'noti_type': notification.noti_type,
                    'type': notification.type,
                    'main_id': notification.main_id,
                    'ref_id': notification.ref_id,
                    'header': notification.header,
                    'detail': notification.detail,
                    'create_date': notification.create_date.isoformat() if notification.create_date else None,
                    'is_read': notification.is_read
                }
                
                # เพิ่มข้อมูลโครงการถ้าเป็นการแจ้งเตือนเกี่ยวกับ matching
                if notification.noti_type == "matching" and notification.ref_id:
                    try:
                        project = TcdAppProject.objects.filter(id=notification.ref_id).first()
                        if project:
                            notification_data['project_name'] = project.name
                    except Exception:
                        pass
                
                notification_list.append(notification_data)
            
            total_count = notifications.count()
            has_next = end_index < total_count
            
            return service_success_response({
                'notifications': notification_list,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total_count,
                    'has_next': has_next
                }
            }, language)
            
        except Exception as e:
            logger.error(f"Error getting member notifications: {str(e)}")
            return service_error_response(5000, language)  # System error
    
    @staticmethod
    def mark_notification_as_read(notification_id: int, app_member_id: int, language: str = 'th') -> Dict:
        """
        ทำเครื่องหมายการแจ้งเตือนว่าอ่านแล้ว
        
        Args:
            notification_id (int): ID ของการแจ้งเตือน
            app_member_id (int): ID ของสมาชิก
            language (str): ภาษา
            
        Returns:
            dict: ผลลัพธ์การอัปเดต
        """
        try:
            notification = TcdAppNotification.objects.filter(
                id=notification_id,
                type="APPMEMBER",
                main_id=app_member_id
            ).first()
            
            if not notification:
                return service_error_response(3002, language)  # Data not found
            
            notification.is_read = True
            notification.save()
            
            logger.info(f"Marked notification {notification_id} as read for member {app_member_id}")
            
            return service_success_response({
                'notification_id': notification_id,
                'is_read': True,
                'message': 'Notification marked as read'
            }, language)
            
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            return service_error_response(5000, language)  # System error
