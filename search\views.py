from rest_framework import viewsets, status
from rest_framework.permissions import AllowAny
from rest_framework.parsers import <PERSON><PERSON><PERSON>ars<PERSON>
from rest_framework.response import Response
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.utils.html import escape
import logging

from utils.pagination import CustomPagination
from utils.response import APIResponse, get_language_from_request

from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample

from .models import TcdSector, TcdSkill, TcdService
from .serializers import (
    TcdSectorSerializer,
    TcdSkillSerializer,
    TcdServiceSerializer,
    SkillFilterRequestSerializer
)

logger = logging.getLogger(__name__)


def sanitize_search_input(input_string, max_length=500):
    """
    Sanitize search input to prevent XSS and injection attacks
    """
    if not input_string:
        return ""
    
    # Remove HTML tags and escape special characters
    sanitized = escape(str(input_string).strip())
    
    # Truncate to max length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def validate_search_parameters(params):
    """
    Validate search parameters
    """
    errors = {}
    
    # Validate sector_ids if provided
    if 'sector_ids' in params and params['sector_ids']:
        try:
            sector_ids = [int(x.strip()) for x in params['sector_ids'].split(',') if x.strip()]
            if not sector_ids:
                errors['sector_ids'] = 'At least one valid sector ID is required'
            elif any(sid <= 0 for sid in sector_ids):
                errors['sector_ids'] = 'All sector IDs must be positive integers'
            elif len(sector_ids) > 50:
                errors['sector_ids'] = 'Too many sector IDs (maximum 50)'
        except ValueError:
            errors['sector_ids'] = 'Invalid sector ID format'
    
    return errors


class TcdSectorViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Enhanced Sector Master Data API with comprehensive security and error handling
    """

    queryset = TcdSector.objects.all().order_by('id')
    serializer_class = TcdSectorSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    authentication_classes = []

    def get_queryset(self):
        """
        Get optimized queryset for sectors
        """
        try:
            return TcdSector.objects.all().order_by('id')
        except Exception as e:
            logger.error(f"Error getting sector queryset: {str(e)}")
            return TcdSector.objects.none()

    def list(self, request, *args, **kwargs):
        """
        List all sectors with pagination and error handling
        """
        try:
            # Get language preference
            language = get_language_from_request(request)
            
            # Get queryset
            queryset = self.get_queryset()
            
            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)
                
                # Convert to APIResponse format
                return APIResponse.success(
                    data={
                        'results': paginated_response.data['data'],
                        'pagination': {
                            'count': paginated_response.data['total'],
                            'next': None,  # Will be handled by frontend based on has_next
                            'previous': None,  # Will be handled by frontend
                            'page_size': paginated_response.data['per_page'],
                            'current_page': paginated_response.data['page'],
                            'has_next': paginated_response.data['has_next']
                        }
                    },
                    language=language
                )
            
            # No pagination
            serializer = self.get_serializer(queryset, many=True)
            return APIResponse.success(serializer.data, language)
            
        except Exception as e:
            logger.error(f"Error in sector list: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )

    def retrieve(self, request, pk=None):
        """
        Retrieve specific sector by ID
        """
        try:
            language = get_language_from_request(request)
            
            # Validate pk
            try:
                sector_id = int(pk)
                if sector_id <= 0:
                    return APIResponse.error(
                        error_code=2000,
                        custom_message="Invalid sector ID",
                        language=language,
                        status_code=400
                    )
            except (ValueError, TypeError):
                return APIResponse.error(
                    error_code=2000,
                    custom_message="Invalid sector ID format",
                    language=language,
                    status_code=400
                )
            
            # Get sector
            try:
                sector = self.get_queryset().get(pk=sector_id)
                serializer = self.get_serializer(sector)
                return APIResponse.success(serializer.data, language)
            except TcdSector.DoesNotExist:
                return APIResponse.error(
                    error_code=3002,
                    custom_message="Sector not found",
                    language=language,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"Error in sector retrieve: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )


class TcdSkillViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Skill Master Data API with sector filtering
    """

    serializer_class = TcdSkillSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    authentication_classes = []

    def get_queryset(self):
        """
        Get queryset filtered by sector_ids parameter
        """
        sector_ids = self.request.GET.get('sector_ids', '')
        
        if not sector_ids:
            return TcdSkill.objects.none()
        
        try:
            # Parse sector IDs
            sector_id_list = [int(x.strip()) for x in sector_ids.split(',') if x.strip()]
            if not sector_id_list:
                return TcdSkill.objects.none()
            
            # Filter by sector IDs and order by st, nd, rd
            return TcdSkill.objects.filter(
                sector_id__in=sector_id_list
            ).order_by('st', 'nd', 'rd')
            
        except ValueError:
            return TcdSkill.objects.none()

    def list(self, request, *args, **kwargs):
        """
        List skills filtered by sector IDs
        """
        try:
            language = get_language_from_request(request)
            
            # Validate sector_ids parameter
            sector_ids = request.GET.get('sector_ids', '')
            if not sector_ids:
                return APIResponse.error(
                    error_code=2000,
                    custom_message="sector_ids parameter is required",
                    language=language,
                    status_code=400
                )
            
            # Validate sector_ids format
            validation_errors = validate_search_parameters({'sector_ids': sector_ids})
            if validation_errors:
                return APIResponse.error(
                    error_code=2000,
                    custom_message=validation_errors.get('sector_ids', 'Invalid sector_ids'),
                    language=language,
                    status_code=400
                )
            
            # Get queryset
            queryset = self.get_queryset()
            
            if not queryset.exists():
                return APIResponse.success(
                    data={
                        'results': [],
                        'pagination': {
                            'count': 0,
                            'next': None,
                            'previous': None,
                            'page_size': self.pagination_class.page_size,
                            'current_page': 1
                        }
                    },
                    language=language
                )
            
            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)
                
                # Convert to APIResponse format
                return APIResponse.success(
                    data={
                        'results': paginated_response.data['data'],
                        'pagination': {
                            'count': paginated_response.data['total'],
                            'next': None,  # Will be handled by frontend based on has_next
                            'previous': None,  # Will be handled by frontend
                            'page_size': paginated_response.data['per_page'],
                            'current_page': paginated_response.data['page'],
                            'has_next': paginated_response.data['has_next']
                        }
                    },
                    language=language
                )
            
            # No pagination
            serializer = self.get_serializer(queryset, many=True)
            return APIResponse.success(serializer.data, language)
            
        except Exception as e:
            logger.error(f"Error in skill list: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )

    def retrieve(self, request, pk=None):
        """
        Retrieve specific skill by ID
        """
        try:
            language = get_language_from_request(request)
            
            # Validate pk
            try:
                skill_id = int(pk)
                if skill_id <= 0:
                    return APIResponse.error(
                        error_code=2000,
                        custom_message="Invalid skill ID",
                        language=language,
                        status_code=400
                    )
            except (ValueError, TypeError):
                return APIResponse.error(
                    error_code=2000,
                    custom_message="Invalid skill ID format",
                    language=language,
                    status_code=400
                )
            
            # Get skill
            try:
                skill = TcdSkill.objects.get(pk=skill_id)
                serializer = self.get_serializer(skill)
                return APIResponse.success(serializer.data, language)
            except TcdSkill.DoesNotExist:
                return APIResponse.error(
                    error_code=3002,
                    custom_message="Skill not found",
                    language=language,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"Error in skill retrieve: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )


class TcdServiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Service Master Data API
    """

    queryset = TcdService.objects.all().order_by('code')
    serializer_class = TcdServiceSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    authentication_classes = []

    def list(self, request, *args, **kwargs):
        """
        List all services with pagination and error handling
        """
        try:
            # Get language preference
            language = get_language_from_request(request)
            
            # Get queryset
            queryset = self.get_queryset()
            
            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                paginated_response = self.get_paginated_response(serializer.data)
                
                # Convert to APIResponse format
                return APIResponse.success(
                    data={
                        'results': paginated_response.data['data'],
                        'pagination': {
                            'count': paginated_response.data['total'],
                            'next': None,  # Will be handled by frontend based on has_next
                            'previous': None,  # Will be handled by frontend
                            'page_size': paginated_response.data['per_page'],
                            'current_page': paginated_response.data['page'],
                            'has_next': paginated_response.data['has_next']
                        }
                    },
                    language=language
                )
            
            # No pagination
            serializer = self.get_serializer(queryset, many=True)
            return APIResponse.success(serializer.data, language)
            
        except Exception as e:
            logger.error(f"Error in service list: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )

    def retrieve(self, request, pk=None):
        """
        Retrieve specific service by ID
        """
        try:
            language = get_language_from_request(request)
            
            # Validate pk
            try:
                service_id = int(pk)
                if service_id <= 0:
                    return APIResponse.error(
                        error_code=2000,
                        custom_message="Invalid service ID",
                        language=language,
                        status_code=400
                    )
            except (ValueError, TypeError):
                return APIResponse.error(
                    error_code=2000,
                    custom_message="Invalid service ID format",
                    language=language,
                    status_code=400
                )
            
            # Get service
            try:
                service = self.get_queryset().get(pk=service_id)
                serializer = self.get_serializer(service)
                return APIResponse.success(serializer.data, language)
            except TcdService.DoesNotExist:
                return APIResponse.error(
                    error_code=3002,
                    custom_message="Service not found",
                    language=language,
                    status_code=404
                )
                
        except Exception as e:
            logger.error(f"Error in service retrieve: {str(e)}")
            language = get_language_from_request(request)
            return APIResponse.error(
                error_code=5000,
                custom_message="Internal server error",
                language=language,
                status_code=500
            )
