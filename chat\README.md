# MCDC Mobile Chat System

A comprehensive real-time chat system built with Django Channels and WebSockets specifically designed for mobile applications in the MCDC project.

## 📋 Table of Contents

1. [🔒 Access Control & Room Ownership](#-access-control--room-ownership)
2. [✨ Features](#-features)
3. [🚀 Installation](#-installation)
4. [⚙️ Configuration](#️-configuration)
5. [📱 Mobile API Endpoints](#-mobile-api-endpoints)
6. [🔌 WebSocket Endpoints](#-websocket-endpoints)
7. [📄 WebSocket Message Types](#-websocket-message-types)
8. [📁 File Upload System](#-file-upload-system)
9. [🕐 Timezone Handling](#-timezone-handling)
10. [📱 Mobile Integration](#-mobile-integration)
11. [🧪 Testing](#-testing)
12. [🔧 Troubleshooting](#-troubleshooting)
13. [🤝 Contributing](#-contributing)

## 🔒 Access Control & Room Ownership

### **Updated Access Control Rules**
The chat system now implements strict access control with the following rules:

- **TcdAppMembers (Regular Members)**: **COMPLETELY BLOCKED** from all chat features
- **TcdUserConsult (Consultants)**: Can own and create chat rooms, send/receive messages
- **TcdUsers (Staff)**: Can join consultant-owned rooms and respond to messages

### **Room Ownership Model**
- **Room Owners**: Only `TcdUserConsult` (consultants) can own and create chat rooms
- **One Room Per Consultant**: Each consultant has exactly ONE chat room (no staff_id required)
- **Open Participation**: Any `TcdUsers` (staff) can join and respond in consultant-owned rooms
- **Data Flow**: One-to-many consultation model where one consultant can receive responses from multiple staff members in a single conversation thread

### **Error Handling for Access Control**
- `APIResponse.unauthorized()`: Returned when TcdAppMembers attempt to access chat features
- `APIResponse.forbidden()`: Returned for invalid user types or insufficient permissions
- All error responses follow established APIResponse patterns with proper language support

## ✨ Features

### 📱 Mobile-First Design
- **Mobile API**: RESTful APIs designed specifically for mobile applications
- **Real-time WebSocket**: Optimized WebSocket connections for mobile devices
- **Push Notifications**: Support for mobile push notifications with FCM integration
- **Offline Support**: Message queuing and synchronization
- **Battery Optimization**: Efficient connection management
- **File Upload**: Direct file upload support for images and documents

### 🚀 Real-time Communication
- **WebSocket Support**: Real-time messaging using Django Channels (In-Memory)
- **Room-based Chat**: Consultation rooms owned by consultants with staff participants
- **Typing Indicators**: Real-time typing status
- **Online Status**: Track user online/offline status (consultants and staff only)
- **Message Read Receipts**: Track message delivery and read status
- **Authentication**: JWT token-based WebSocket authentication

### 💬 Chat Functionality
- **Text Messages**: Send and receive text messages (consultants and staff only)
- **File Sharing**: Upload and share files with validation and size restrictions
  - **Images**: JPEG, JPG, PNG (max 1MB)
  - **Documents**: PDF, DOC, DOCX, XLS, XLSX (max 5MB)
- **Message History**: Persistent message storage with pagination and filtering
- **Chat Sessions**: Session management for mobile applications
- **Quick Replies**: Pre-defined chat prompts for common responses
- **File Management**: Download, metadata retrieval, and file listing

### 🔐 Security & Authentication
- **JWT Authentication**: Secure authentication using JWT tokens with RS256 algorithm
- **Strict Access Control**: Consultant/Staff-only permission system
- **User Type Validation**: Custom validation function for chat access
- **Secure WebSocket**: Authenticated WebSocket connections with token validation
- **File Access Control**: Role-based file access permissions

### 🌐 Multi-language Support
- **Thai/English**: Full support for Thai and English languages
- **Localized Responses**: Error messages and content in user's preferred language
- **Date Formatting**: Consistent date formatting across all responses

## 🚀 Installation

### 1. Install Dependencies

```bash
pip install channels==4.0.0
pip install daphne==4.1.0
pip install pytz  # For timezone handling
```

**Note**: Redis is not required as the system uses In-Memory Channel Layer for simplicity.

### 2. Database Setup
**No migration required!** The system uses existing database tables:
- `tcd_chat` - Main chat messages table with file support
- `tcd_chat_prompt` - Quick reply prompts
- `tcd_setting_chat` - Chat system settings and operating hours

The mobile chat system is designed to work with your existing database structure.

### 3. Environment Variables
Set up the following environment variables:

```bash
# File Upload Configuration
UPLOAD_DIR=/path/to/uploads  # Default: 'uploads'
MEDIA_PREFIX=/files/         # Default: '/media/'
BASE_URL=http://localhost:8000

# WebSocket Configuration
WEBSOCKET_HOST=localhost     # Default: 'localhost'
WEBSOCKET_PORT=8080          # Default: '8080'
WEBSOCKET_PROTOCOL=ws        # Default: 'ws' (use 'wss' for SSL)

# Timezone Configuration
TZ=Asia/Bangkok              # GMT+7 Thailand timezone
```

## ⚙️ Configuration

### Settings Configuration
The following settings have been added to `MCDC/settings.py`:

```python
# Add to INSTALLED_APPS
INSTALLED_APPS = [
    "daphne",  # Add at the top
    # ... other apps
    "channels",
    # ... rest of apps
]

# ASGI Configuration
ASGI_APPLICATION = "MCDC.asgi.application"

# Channels Configuration (In-Memory)
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Timezone Configuration
USE_TZ = True

# File Upload Configuration
UPLOAD_DIR = os.environ.get('UPLOAD_DIR', 'uploads')
MEDIA_ROOT = os.path.join(BASE_DIR, UPLOAD_DIR)
MEDIA_PREFIX = os.environ.get('MEDIA_PREFIX', '/media/')
BASE_URL = os.environ.get('BASE_URL', 'http://localhost:8000')
MEDIA_URL = BASE_URL + MEDIA_PREFIX

# WebSocket Configuration
WEBSOCKET_HOST = os.environ.get('WEBSOCKET_HOST', 'localhost')
WEBSOCKET_PORT = os.environ.get('WEBSOCKET_PORT', '8080')
WEBSOCKET_PROTOCOL = os.environ.get('WEBSOCKET_PROTOCOL', 'ws')  # 'ws' or 'wss'
WEBSOCKET_BASE_URL = f"{WEBSOCKET_PROTOCOL}://{WEBSOCKET_HOST}:{WEBSOCKET_PORT}"

# JWT Configuration for WebSocket Authentication
JWT_PRIVATE_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'private_key.pem')
JWT_PUBLIC_KEY_PATH = os.path.join(BASE_DIR, 'keys', 'public_key.pem')
```

## 📁 File Upload System

The chat system supports comprehensive file upload functionality with strict validation and security measures.

### File Type Support

#### Images
- **Allowed Types**: JPEG, JPG, PNG
- **MIME Types**: `image/jpeg`, `image/jpg`, `image/png`
- **Extensions**: `.jpg`, `.jpeg`, `.png`
- **Maximum Size**: 1MB
- **Message Type**: `I` (Image)

#### Documents
- **Allowed Types**: PDF, Word, Excel
- **MIME Types**:
  - `application/pdf`
  - `application/msword`
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - `application/vnd.ms-excel`
  - `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Extensions**: `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`
- **Maximum Size**: 5MB
- **Message Type**: `D` (Document)

### File Storage Structure

Files are stored in the `UPLOAD_DIR` with the following structure:
```
uploads/
├── images/
│   ├── abc123def456.jpg
│   └── xyz789uvw012.png
└── documents/
    ├── def456ghi789.pdf
    └── jkl012mno345.docx
```

### File Upload Methods

#### 1. Direct File Upload (Recommended)
Upload files directly through the `/api/chat/send/` endpoint:

```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "message=Here's the document you requested" \
  -F "file=@/path/to/document.pdf" \
  -F "recipient_id=123" \
  -F "recipient_type=consultant" \
  http://localhost:8080/api/chat/send/
```

#### 2. File Reference Upload
Upload by providing file name and source path:

```json
{
    "message": "Please review this analysis",
    "file_name": "analysis_report.pdf",
    "file_src": "uploads/documents/analysis_report.pdf",
    "message_type": "D",
    "recipient_id": 123,
    "recipient_type": "consultant"
}
```

### File Access and Security

- **Access Control**: Only consultants and staff can upload/download files
- **URL Generation**: Files are served through secure static file URLs
- **File Validation**: Comprehensive validation for type, size, and content
- **Unique Naming**: Files are renamed with UUID to prevent conflicts

### File Management Endpoints

#### List Files
```
GET /api/chat/files/
```
Get list of files with filtering options.

#### Download File
```
GET /api/chat/file/download/{message_id}/
```
Download file from a specific chat message.

#### File Metadata
```
GET /api/chat/file/metadata/{message_id}/
```
Get detailed file information and metadata.

## 🕐 Timezone Handling

The chat system uses Django's timezone handling based on your application's timezone configuration.

### Configuration
```python
# settings.py
USE_TZ = True
```

### Date Formatting
All API responses include formatted dates:

```json
{
    "date": "2024-01-15T10:30:00Z"
}
```

### WebSocket Messages
WebSocket messages include timestamps:

```json
{
    "type": "message",
    "data": {
        "message": "Hello",
        "date": "2024-01-15T10:30:00Z"
    }
}
```

## 📱 Mobile API Endpoints

**⚠️ Access Control**: All chat endpoints now require users to be either `TcdUserConsult` (consultants) or `TcdUsers` (staff). `TcdAppMembers` (regular members) are completely blocked and will receive `APIResponse.unauthorized()`.

### Start Chat Session
```
POST /api/chat/start/
```
Start a new chat session. Consultants create their own room, staff can join any consultant's room.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can create their own room (no staff_id required)
- ✅ `TcdUsers` (staff) - Can join any consultant's room
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Body for Consultants (Creating Room):**
```json
{
    "chat_type": "consultation",
    "device_token": "fcm_device_token_here"
}
```
*Note: No staff_id required! Any staff can join and respond.*

**Body for Staff (Joining Room):**
```json
{
    "consultant_id": 123,  // TcdUserConsult ID (required for staff)
    "chat_type": "consultation",
    "device_token": "fcm_device_token_here"
}
```

**Response:**
```json
{
    "status": true,
    "data": {
        "room": {
            "room_id": "consultation_123",
            "room_name": "Consultation Room - Dr. Smith",
            "room_type": "consultation",
            "owner": {
                "id": 123,
                "name": "Dr. Smith",
                "type": "consultant"
            },
            "participant_info": [
                {
                    "id": 789,
                    "name": "John Smith",
                    "type": "staff",
                    "position": "Senior Analyst"
                },
                {
                    "id": 790,
                    "name": "Jane Doe",
                    "type": "staff",
                    "position": "Junior Analyst"
                }
            ],
            "total_participants": 2,
            "allows_any_staff": true,
            "last_message": null,
            "unread_count": 0,
            "created_at": "2024-01-15T10:30:00Z"
        },
        "session_id": "consultation_123_123_consultant",
        "websocket_url": "ws://localhost:8080/ws/chat/room/consultation_123/",
        "created": true
    }
}
```

**Error Response for Blocked Members:**
```json
{
    "status": false,
    "error_code": 401,
    "error_message": "Unauthorized access",
    "data": {}
}
```

### Get Chat Rooms
```
GET /api/chat/rooms/
```
Get available chat rooms for the authenticated user.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Get rooms they own with staff participants
- ✅ `TcdUsers` (staff) - Get consultant rooms they participate in
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Response for Consultants:**
```json
{
    "status": true,
    "data": {
        "rooms": [
            {
                "room_id": "consultation_123",
                "room_name": "Consultation Room - Dr. Smith",
                "room_type": "consultation",
                "owner": {
                    "id": 123,
                    "name": "Dr. Smith",
                    "type": "consultant"
                },
                "participants": [
                    {
                        "id": 789,
                        "name": "John Smith",
                        "type": "staff",
                        "position": "Senior Analyst"
                    },
                    {
                        "id": 790,
                        "name": "Jane Doe",
                        "type": "staff",
                        "position": "Junior Analyst"
                    }
                ],
                "total_participants": 2,
                "allows_any_staff": true,
                "last_message": {
                    "id": 789,
                    "message": "Hello, how can I help you?",
                    "date": "2024-01-15T10:30:00Z",
                    "sender_type": "staff",
                    "sender_name": "John Smith"
                },
                "unread_count": 2
            }
        ]
    }
}
```

**Response for Staff:**
```json
{
    "status": true,
    "data": {
        "rooms": [
            {
                "room_id": "consultation_123",
                "room_name": "Chat with Dr. Smith",
                "room_type": "consultation",
                "owner": {
                    "id": 123,
                    "name": "Dr. Smith",
                    "type": "consultant"
                },
                "participants": [
                    {
                        "id": 789,
                        "name": "John Smith",
                        "type": "staff",
                        "position": "Senior Analyst"
                    },
                    {
                        "id": 790,
                        "name": "Jane Doe",
                        "type": "staff",
                        "position": "Junior Analyst"
                    }
                ],
                "total_participants": 2,
                "allows_any_staff": true,
                "last_message": {
                    "id": 790,
                    "message": "I'll review this case",
                    "date": "2024-01-15T10:35:00Z",
                    "sender_type": "consultant",
                    "sender_name": "Dr. Smith"
                },
                "unread_count": 1
            }
        ]
    }
}
```

### Chat History
```
GET /api/chat/history/
```
Get chat message history with pagination and role-based access control.

**Access Control & Usage Patterns**:

#### For Consultants (TcdUserConsult)
- ✅ **Automatic Access**: Can access their own room history without parameters
- ✅ **Filter by Staff**: Can filter messages by specific staff member using `user_id`
- ✅ **Full History**: Access to all messages in their consultation room

**Example for Consultants:**
```bash
# Get all messages in consultant's own room
GET /api/chat/history/

# Filter by specific staff member
GET /api/chat/history/?user_id=789

# Date range filtering
GET /api/chat/history/?date_from=2024-01-01&date_to=2024-01-31
```

#### For Staff (TcdUsers)
- ✅ **Parameter Required**: Must provide `user_consult_id` to specify consultant's room
- ✅ **Room Access**: Can access history of consultant rooms they participate in
- ✅ **Same Filtering**: Can use same filtering options as consultants

**Example for Staff:**
```bash
# Staff must specify consultant's room
GET /api/chat/history/?user_consult_id=123

# Staff can also filter by date
GET /api/chat/history/?user_consult_id=123&date_from=2024-01-01
```

#### Blocked Access
- ❌ `TcdAppMembers` (members) - **COMPLETELY BLOCKED**

**Parameters:**
- `user_consult_id` (required for staff): Consultant ID to access their room history
- `user_id` (optional): TcdUsers (staff) ID to filter messages
- `page` (default: 1): Page number
- `per_page` (default: 20): Messages per page
- `date_from` (optional): Start date filter (YYYY-MM-DD format)
- `date_to` (optional): End date filter (YYYY-MM-DD format)

**Response:**
```json
{
    "status": true,
    "data": {
        "messages": [
            {
                "id": 123,
                "type": "T",
                "message": "Hello, I need assistance with this case",
                "file_name": null,
                "file_src": null,
                "date": "2024-01-15T10:30:00Z",
                "sender_type": "staff",
                "sender_info": {
                    "id": 789,
                    "name": "John Smith",
                    "type": "staff"
                }
            }
        ],
        "pagination": {
            "page": 1,
            "per_page": 20,
            "total": 45,
            "has_next": true,
            "has_previous": false,
            "total_pages": 3
        }
    }
}
```

### Send Message
```
POST /api/chat/send/
```
Send a new chat message. Both consultants and staff can send messages.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can send messages to staff in their rooms
- ✅ `TcdUsers` (staff) - Can send messages to consultants in rooms they've joined
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Body for Consultants:**
```json
{
    "message": "I've reviewed your case, here are my recommendations",
    "file_name": "analysis_report.pdf",
    "file_src": "uploads/analysis_report.pdf",
    "message_type": "T",
    "recipient_id": 789,  // TcdUsers (staff) ID (optional - message goes to room)
    "recipient_type": "staff"
}
```
*Note: recipient_id is optional for consultants. If not provided, message goes to their room for any staff to see.*

**Body for Staff:**
```json
{
    "message": "Thank you for the guidance, I'll implement these changes",
    "file_name": null,
    "file_src": null,
    "message_type": "T",
    "recipient_id": 123,  // TcdUserConsult (consultant) ID (required)
    "recipient_type": "consultant"
}
```

**Response:**
```json
{
    "status": true,
    "data": {
        "message": {
            "id": 456,
            "type": "T",
            "message": "Thank you for the guidance",
            "file_name": null,
            "file_src": null,
            "date": "2024-01-15T10:45:00Z",
            "sender_type": "staff"
        }
    }
}
```

### Mark as Read
```
POST /api/chat/mark-read/
```
Mark specific messages as read.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can mark messages in their rooms as read
- ✅ `TcdUsers` (staff) - Can mark messages they received as read
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Body:**
```json
{
    "message_ids": [1, 2, 3, 4, 5]
}
```

**Response:**
```json
{
    "status": true,
    "data": {
        "updated_count": 5
    }
}
```

### Mark All as Read
```
POST /api/chat/mark-all-read/
```
Mark all unread messages as read for the authenticated user.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Marks all unread messages in their own room as read (consult_read = '1')
- ✅ `TcdUsers` (staff) - Marks all unread messages as read for a specific consultant's room (users_read = '1')
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Body for Consultants:**
```json
{}
```
*Note: Empty body - consultant marks all their own unread messages*

**Body for Staff:**
```json
{
    "user_consult_id": 123
}
```
*Note: user_consult_id is required for staff to specify which consultant's room*

**Response:**
```json
{
    "status": true,
    "data": {
        "updated_count": 15,
        "user_type": "consultant",
        "message": "Marked 15 messages as read"
    }
}
```

**Error Responses:**
```json
{
    "status": false,
    "error_code": 400,
    "error_message": "user_consult_id is required for staff users",
    "data": {}
}
```

```json
{
    "status": false,
    "error_code": 401,
    "error_message": "You don't have access to this consultant's chat room",
    "data": {}
}
```

### Update Online Status
```
POST /api/chat/online-status/
```
Update user online status.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can update their online status
- ✅ `TcdUsers` (staff) - Can update their online status
- ❌ `TcdAppMembers` (members) - **BLOCKED**

**Body:**
```json
{
    "session_id": "consultation_123_789_123_consultant",
    "is_online": true
}
```

**Response:**
```json
{
    "status": true,
    "data": {
        "status": "updated",
        "session_id": "consultation_123_789_123_consultant",
        "is_online": true,
        "user_type": "consultant",
        "message": "Online status updated (using existing models)"
    }
}
```

### Chat Prompts
```
GET /api/chat/prompts/
```
Get available quick reply prompts.

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can access chat prompts
- ✅ `TcdUsers` (staff) - Can access chat prompts
- ❌ `TcdAppMembers` (members) - **BLOCKED**

### Chat Settings
```
GET /api/chat/settings/
```
Get chat system settings (operating hours, etc.).

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can access chat settings
- ✅ `TcdUsers` (staff) - Can access chat settings
- ❌ `TcdAppMembers` (members) - **BLOCKED**

## 🔌 WebSocket Endpoints

**⚠️ Access Control**: WebSocket connections enforce strict access control with JWT authentication. Only consultants and staff can establish WebSocket connections.

### WebSocket Authentication

All WebSocket connections require JWT token authentication passed as a query parameter:

```
ws://localhost:8080/ws/chat/room/{room_id}/?token=YOUR_JWT_TOKEN
```

#### Authentication Flow
1. **Token Extraction**: JWT token is extracted from the `token` query parameter
2. **Token Validation**: Token is validated using RS256 algorithm with public key
3. **User Identification**: User is identified and loaded from the token payload
4. **Access Validation**: User type and chat access permissions are verified
5. **Connection Establishment**: Connection is established if all validations pass

#### Authentication Errors
- **4001**: Unauthorized (invalid or missing token)
- **4003**: Forbidden (user type not allowed for chat)
- **4004**: Not Found (user not found)

### Mobile Chat Room
```
{WEBSOCKET_BASE_URL}/ws/chat/room/{room_id}/?token=JWT_TOKEN
```
Main WebSocket endpoint for mobile chat rooms with authentication.

**Default URL**: `ws://localhost:8080/ws/chat/room/{room_id}/?token=JWT_TOKEN`

**Access Control**:
- ✅ `TcdUserConsult` (consultants) - Can connect to rooms they own
- ✅ `TcdUsers` (staff) - Can connect to rooms they participate in
- ❌ `TcdAppMembers` (members) - **CONNECTION BLOCKED**

**Room ID Format**: `consultation_{consultant_id}`

**Connection Example:**
```javascript
// Get token from your authentication system
const token = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...";
const roomId = "consultation_123";

// WebSocket URL is now configurable via environment variables
// Default: ws://localhost:8080/ws/chat/room/${roomId}/?token=${token}
// Production: wss://your-domain.com/ws/chat/room/${roomId}/?token=${token}
const wsUrl = `${WEBSOCKET_BASE_URL}/ws/chat/room/${roomId}/?token=${token}`;

const ws = new WebSocket(wsUrl);

ws.onopen = function(event) {
    console.log('Connected to chat room');
};

ws.onerror = function(event) {
    console.error('WebSocket error:', event);
};

ws.onclose = function(event) {
    if (event.code === 4001) {
        console.error('Authentication failed');
    } else if (event.code === 4003) {
        console.error('Access forbidden');
    }
};
```

### Chat Session
```
{WEBSOCKET_BASE_URL}/ws/chat/session/{session_id}/?token=JWT_TOKEN
```
Session-specific WebSocket for individual user sessions.

**Default URL**: `ws://localhost:8080/ws/chat/session/{session_id}/?token=JWT_TOKEN`

**Session ID Format**: `{room_id}_{user_id}_{user_type}`

**Access Control**: Same restrictions as chat room connections.

## 📄 WebSocket Message Types

### Send Text Message (Consultant)
```json
{
    "type": "message",
    "message": "I've reviewed your case analysis",
    "user_id": 123,
    "user_type": "consultant"
}
```

### Send Text Message (Staff)
```json
{
    "type": "message",
    "message": "Thank you for the feedback",
    "user_id": 789,
    "user_type": "staff"
}
```

### Send Message with File (Consultant)
```json
{
    "type": "message",
    "message": "Here's the detailed analysis report",
    "user_id": 123,
    "user_type": "consultant",
    "file_data": {
        "name": "analysis_report.pdf",
        "data": "base64_encoded_file_content",
        "type": "application/pdf"
    }
}
```

### Send Image Message (Staff)
```json
{
    "type": "message",
    "message": "Screenshot of the issue",
    "user_id": 789,
    "user_type": "staff",
    "file_data": {
        "name": "screenshot.png",
        "data": "base64_encoded_image_content",
        "type": "image/png"
    }
}
```

### File Upload Response
When a file is successfully uploaded via WebSocket:
```json
{
    "type": "message_sent",
    "data": {
        "id": 456,
        "type": "D",
        "message": "Here's the detailed analysis report",
        "file_name": "analysis_report.pdf",
        "file_src": "http://localhost:8080/files/documents/abc123def456.pdf",
        "date": "2024-01-15T10:30:00+07:00",
        "sender_type": "consultant",
        "sender_name": "Dr. Smith"
    }
}
```

### Typing Indicator (Consultant)
```json
{
    "type": "typing",
    "user_id": 123,
    "user_type": "consultant",
    "is_typing": true
}
```

### Typing Indicator (Staff)
```json
{
    "type": "typing",
    "user_id": 789,
    "user_type": "staff",
    "is_typing": true
}
```

### Read Receipt
```json
{
    "type": "read_receipt",
    "message_id": 456,
    "user_id": 123,
    "user_type": "consultant"
}
```

### Join Room (Consultant)
```json
{
    "type": "join_room",
    "user_id": 123,
    "user_type": "consultant",
    "room_ownership": "owner"
}
```

### Join Room (Staff)
```json
{
    "type": "join_room",
    "user_id": 789,
    "user_type": "staff",
    "room_ownership": "participant"
}
```

## 📱 Mobile Integration

### Mobile App Flow

**⚠️ Important**: Only consultants and staff can use the mobile chat features. Regular members will be blocked at the authentication level.

1. **Authentication**: User logs in and receives JWT token
2. **Access Validation**: System validates user type (consultant or staff only)
3. **Start Chat**: Call `/api/chat/start/` to create/join chat room
4. **WebSocket Connection**: Connect to WebSocket using room_id with token authentication
5. **Real-time Messaging**: Send/receive messages via WebSocket
6. **File Upload**: Handle file uploads with validation and progress tracking
7. **Push Notifications**: Integrate FCM for background notifications
8. **Background Sync**: Handle offline messages and synchronization

### Complete Mobile Integration Guide

#### 1. Authentication Setup
```javascript
// Login and get JWT token
const loginResponse = await fetch('/api/login/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: '<EMAIL>',
        password: 'password123'
    })
});

const authData = await loginResponse.json();
const jwtToken = authData.data.access_token;

// Store token securely
localStorage.setItem('jwt_token', jwtToken);
```

#### 2. Chat Room Setup
```javascript
// Start chat session
const startChat = async (userType, consultantId = null) => {
    const token = localStorage.getItem('jwt_token');

    const requestBody = userType === 'consultant'
        ? { chat_type: 'consultation', device_token: fcmToken }
        : { consultant_id: consultantId, chat_type: 'consultation', device_token: fcmToken };

    const response = await fetch('/api/chat/start/', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });

    const chatData = await response.json();

    if (!chatData.status) {
        throw new Error(chatData.error_message);
    }

    return chatData.data;
};
```

#### 3. WebSocket Connection with Authentication
```javascript
class ChatWebSocket {
    constructor(roomId, token) {
        this.roomId = roomId;
        this.token = token;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    connect() {
        const wsUrl = `ws://localhost:8080/ws/chat/room/${this.roomId}/?token=${this.token}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = this.onOpen.bind(this);
        this.ws.onmessage = this.onMessage.bind(this);
        this.ws.onclose = this.onClose.bind(this);
        this.ws.onerror = this.onError.bind(this);
    }

    onOpen(event) {
        console.log('Connected to chat room:', this.roomId);
        this.reconnectAttempts = 0;

        // Send join room message
        this.send({
            type: 'join_room',
            user_id: getCurrentUserId(),
            user_type: getCurrentUserType()
        });
    }

    onMessage(event) {
        const data = JSON.parse(event.data);

        switch(data.type) {
            case 'message':
                this.handleNewMessage(data.data);
                break;
            case 'typing':
                this.handleTypingIndicator(data.data);
                break;
            case 'read_receipt':
                this.handleReadReceipt(data.data);
                break;
            case 'error':
                this.handleError(data.message);
                break;
        }
    }

    onClose(event) {
        console.log('WebSocket closed:', event.code, event.reason);

        if (event.code === 4001) {
            console.error('Authentication failed');
            // Redirect to login
        } else if (event.code === 4003) {
            console.error('Access forbidden');
            // Show access denied message
        } else if (this.reconnectAttempts < this.maxReconnectAttempts) {
            // Attempt reconnection
            setTimeout(() => {
                this.reconnectAttempts++;
                this.connect();
            }, 1000 * Math.pow(2, this.reconnectAttempts));
        }
    }

    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        }
    }

    sendMessage(message, fileData = null) {
        this.send({
            type: 'message',
            message: message,
            user_id: getCurrentUserId(),
            user_type: getCurrentUserType(),
            file_data: fileData
        });
    }

    sendTyping(isTyping) {
        this.send({
            type: 'typing',
            user_id: getCurrentUserId(),
            user_type: getCurrentUserType(),
            is_typing: isTyping
        });
    }
}
```

#### 4. File Upload Integration
```javascript
class FileUploadManager {
    constructor(apiToken) {
        this.apiToken = apiToken;
    }

    async uploadFile(file, message, recipientId, recipientType) {
        // Validate file before upload
        const validation = this.validateFile(file);
        if (!validation.valid) {
            throw new Error(validation.error);
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('message', message);
        formData.append('recipient_id', recipientId);
        formData.append('recipient_type', recipientType);

        const response = await fetch('/api/chat/send/', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiToken}`
            },
            body: formData
        });

        const result = await response.json();

        if (!result.status) {
            throw new Error(result.error_message);
        }

        return result.data.message;
    }

    validateFile(file) {
        const isImage = file.type.startsWith('image/');
        const maxSize = isImage ? 1024 * 1024 : 5 * 1024 * 1024; // 1MB for images, 5MB for documents

        if (file.size > maxSize) {
            return {
                valid: false,
                error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
            };
        }

        const allowedTypes = isImage
            ? ['image/jpeg', 'image/jpg', 'image/png']
            : ['application/pdf', 'application/msword',
               'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
               'application/vnd.ms-excel',
               'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

        if (!allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: `Invalid file type. Allowed: ${allowedTypes.join(', ')}`
            };
        }

        return { valid: true };
    }

    // Convert file to base64 for WebSocket upload
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                const base64 = reader.result.split(',')[1]; // Remove data:type;base64, prefix
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    }

    async uploadViaWebSocket(ws, file, message) {
        const base64Data = await this.fileToBase64(file);

        ws.sendMessage(message, {
            name: file.name,
            data: base64Data,
            type: file.type
        });
    }
}
```

#### 5. Push Notification Integration
```javascript
// FCM Token Management
const initializePushNotifications = async () => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            const token = await getMessagingToken(registration);

            // Send token to server when starting chat
            return token;
        } catch (error) {
            console.error('Push notification setup failed:', error);
            return null;
        }
    }
};

// Handle background messages
self.addEventListener('push', function(event) {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            data: data.data
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});
```

### Example Mobile Integration (Consultant)

```javascript
// 1. Start chat session (Consultant creating their own room)
const startChatResponse = await fetch('/api/chat/start/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + consultantToken,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        // No staff_id required! Any staff can join and respond
        chat_type: 'consultation',
        device_token: fcmToken
    })
});

const chatData = await startChatResponse.json();

// Check for access control errors
if (!chatData.status) {
    console.error('Access denied:', chatData.error_message);
    return;
}

// Room ID is now: consultation_{consultant_id}
console.log('Room ID:', chatData.data.room.room_id); // consultation_123
console.log('Allows any staff:', chatData.data.room.allows_any_staff); // true

// 2. Connect to WebSocket
const ws = new WebSocket(chatData.data.websocket_url);

// 3. Handle messages
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'message') {
        displayMessage(data.data);
    }
};

// 4. Send message (Consultant to room - any staff can see)
const sendMessage = (text) => {
    ws.send(JSON.stringify({
        type: 'message',
        message: text,
        user_id: currentUserId,
        user_type: 'consultant'
    }));
};
```

### Example Mobile Integration (Staff)

```javascript
// 1. Start chat session (Staff joining consultant room)
const startChatResponse = await fetch('/api/chat/start/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + staffToken,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        consultant_id: 123,  // TcdUserConsult ID
        chat_type: 'consultation',
        device_token: fcmToken
    })
});

const chatData = await startChatResponse.json();

// 2. Send message (Staff to Consultant)
const sendMessage = (text) => {
    ws.send(JSON.stringify({
        type: 'message',
        message: text,
        user_id: currentUserId,
        user_type: 'staff'
    }));
};
```

### Error Handling for Blocked Members

```javascript
// Example of handling blocked member access
const response = await fetch('/api/chat/start/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + memberToken,  // This will fail
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        consultant_id: 123
    })
});

const result = await response.json();

if (!result.status && result.error_code === 401) {
    // Member is blocked from chat features
    showErrorMessage('Chat access is restricted to consultants and staff only');
}
```

## Running the Application

### Development Server
```bash
# Start Django development server with ASGI
python manage.py runserver
```

### Production Deployment
For production, use Daphne or another ASGI server:

```bash
# Using Daphne
daphne -b 0.0.0.0 -p 8080 MCDC.asgi:application

# Using Gunicorn with Uvicorn workers
gunicorn MCDC.asgi:application -w 4 -k uvicorn.workers.UvicornWorker
```

## 🧪 Testing

### Test Mobile API
Test the mobile API endpoints using tools like Postman, curl, or automated testing frameworks.

**⚠️ Testing Access Control**: Make sure to test with different user types to verify access control is working correctly.

### API Testing Examples

#### File Upload Testing
```bash
# Test image upload (should work - max 1MB)
curl -X POST \
     -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     -F "message=Here's a screenshot" \
     -F "file=@test_image.png" \
     -F "recipient_id=789" \
     -F "recipient_type=staff" \
     http://localhost:8080/api/chat/send/

# Test document upload (should work - max 5MB)
curl -X POST \
     -H "Authorization: Bearer STAFF_JWT_TOKEN" \
     -F "message=Analysis report attached" \
     -F "file=@report.pdf" \
     -F "recipient_id=123" \
     -F "recipient_type=consultant" \
     http://localhost:8080/api/chat/send/

# Test oversized file (should fail)
curl -X POST \
     -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     -F "message=Large file test" \
     -F "file=@large_file.pdf" \
     http://localhost:8080/api/chat/send/
```

#### Chat History Access Testing
```bash
# Consultant accessing own history (should work)
curl -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     "http://localhost:8080/api/chat/history/"

# Staff accessing consultant history with parameter (should work)
curl -H "Authorization: Bearer STAFF_JWT_TOKEN" \
     "http://localhost:8080/api/chat/history/?user_consult_id=123"

# Staff accessing history without parameter (should fail)
curl -H "Authorization: Bearer STAFF_JWT_TOKEN" \
     "http://localhost:8080/api/chat/history/"
```

#### WebSocket Connection Testing
```javascript
// Test WebSocket authentication
const testWebSocketAuth = (token, roomId) => {
    const ws = new WebSocket(`ws://localhost:8080/ws/chat/room/${roomId}/?token=${token}`);

    ws.onopen = () => console.log('✅ WebSocket connected successfully');
    ws.onclose = (event) => {
        if (event.code === 4001) console.error('❌ Authentication failed');
        if (event.code === 4003) console.error('❌ Access forbidden');
    };
    ws.onerror = (error) => console.error('❌ WebSocket error:', error);
};

// Test with different user types
testWebSocketAuth(consultantToken, 'consultation_123');
testWebSocketAuth(staffToken, 'consultation_123');
testWebSocketAuth(memberToken, 'consultation_123'); // Should fail
```

#### Test with Consultant Token (Should Work)
```bash
# Start chat session (Consultant creating their own room - no staff_id needed)
curl -X POST \
     -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"chat_type": "consultation"}' \
     http://localhost:8080/api/chat/start/

# Get chat rooms (Consultant)
curl -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     http://localhost:8080/api/chat/rooms/

# Send message (Consultant to room - any staff can see)
curl -X POST \
     -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello from consultant! Any staff can respond."}' \
     http://localhost:8080/api/chat/send/

# Send message to specific staff (optional)
curl -X POST \
     -H "Authorization: Bearer CONSULTANT_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello John!", "recipient_id": 789, "recipient_type": "staff"}' \
     http://localhost:8080/api/chat/send/
```

#### Test with Staff Token (Should Work)
```bash
# Start chat session (Staff joining consultant room)
curl -X POST \
     -H "Authorization: Bearer STAFF_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"consultant_id": 123, "chat_type": "consultation"}' \
     http://localhost:8080/api/chat/start/

# Send message (Staff to Consultant)
curl -X POST \
     -H "Authorization: Bearer STAFF_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello from staff!", "recipient_id": 123, "recipient_type": "consultant"}' \
     http://localhost:8080/api/chat/send/
```

#### Test with Member Token (Should Be Blocked)
```bash
# This should return 401 Unauthorized
curl -X POST \
     -H "Authorization: Bearer MEMBER_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"consultant_id": 123, "chat_type": "consultation"}' \
     http://localhost:8080/api/chat/start/

# Expected response:
# {
#   "status": false,
#   "error_code": 401,
#   "error_message": "Unauthorized access",
#   "data": {}
# }
```

### Testing Access Control Validation

1. **Test Member Blocking**: Verify all endpoints return 401 for TcdAppMembers
2. **Test Consultant Access**: Verify consultants can create rooms and send messages
3. **Test Staff Access**: Verify staff can join rooms and respond to messages
4. **Test Room Ownership**: Verify only consultants can access chat history

**Important**: No database migration is needed! The system uses your existing `tcd_chat` table.

## Data Flow Documentation

### Updated One-to-Many Consultation Model

The chat system now implements a simplified one-to-many consultation model:

```
TcdUserConsult (Consultant)
    ↓ (owns ONE room)
Chat Room (consultation_{consultant_id})
    ↓ (any staff can join)
Multiple TcdUsers (Staff)
    ↓ (completely blocked)
TcdAppMembers (Members) ❌
```

### Room Ownership Flow

1. **Room Creation**: Only `TcdUserConsult` (consultants) can create chat rooms (no staff_id required)
2. **One Room Per Consultant**: Each consultant has exactly ONE room with ID `consultation_{consultant_id}`
3. **Open Staff Participation**: ANY `TcdUsers` (staff) can join and respond in consultant rooms
4. **Unified Conversation**: All staff responses to a consultant are in the same conversation thread
5. **Message Flow**:
   - Consultant → Room: Messages visible to all staff who join
   - Staff → Consultant: Responses in consultant's room
   - Multiple Staff → Consultant: All responses in same conversation
   - Member → Anyone: **BLOCKED** at API level

### Database Relationships

- **`tcd_chat.user_consult_id`**: References the consultant who owns the room
- **`tcd_chat.users_id`**: References the staff member who sent the message
- **Conversation Thread**: All messages with same `user_consult_id` belong to same conversation
- **Access Control**: Enforced at the application level, not database level

### Key Benefits

1. **Simplified Room Management**: No need to specify staff when creating rooms
2. **Flexible Staff Assignment**: Any available staff can help any consultant
3. **Unified Conversations**: All responses to a consultant are in one place
4. **Scalable Design**: Supports multiple staff members helping one consultant
5. **Better Collaboration**: Staff can see each other's responses and collaborate

## 🔧 Troubleshooting

### Common Issues

#### 1. WebSocket Connection Issues

**Problem**: WebSocket connection fails or closes immediately
```
WebSocket connection to 'ws://localhost:8080/ws/chat/room/consultation_123/' failed
```

**Solutions**:
- Verify ASGI configuration in `settings.py`
- Check if Daphne is properly installed: `pip install daphne==4.1.0`
- Ensure WebSocket URLs are correct and include authentication token
- Verify the server is running with ASGI support: `python manage.py runserver`

**Debug Steps**:
```bash
# Check if WebSocket endpoint is accessible
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: test" \
     http://localhost:8080/ws/chat/room/consultation_123/
```

#### 2. Authentication Issues

**Problem**: JWT token authentication fails
```json
{
    "type": "error",
    "message": "Authentication failed",
    "code": 4001
}
```

**Solutions**:
- Verify JWT token is valid and not expired
- Check token format: `Bearer <token>` for HTTP, `?token=<token>` for WebSocket
- Ensure JWT keys are properly configured in settings
- Verify user exists and has correct user_type attribute

**Debug Steps**:
```python
# Test JWT token in Django shell
import jwt
from django.conf import settings

# Decode token to check payload
token = "your_jwt_token_here"
try:
    payload = jwt.decode(token, settings.JWT_PUBLIC_KEY, algorithms=['RS256'])
    print("Token payload:", payload)
except jwt.ExpiredSignatureError:
    print("Token has expired")
except jwt.InvalidTokenError as e:
    print("Invalid token:", str(e))
```

#### 3. Access Control Issues

**Problem**: User access denied despite valid authentication

**Error Codes**:
- **401 Unauthorized**: User is a TcdAppMember (blocked from chat)
- **403 Forbidden**: Invalid user type or missing user_type attribute
- **404 Not Found**: Trying to access non-existent consultant or staff member

**Solutions**:
```python
# Check user type in Django shell
from authentication.models import TcdUserConsult, TcdAppMember
from MCDC.models import TcdUsers

# Verify user type
user_id = 123
try:
    consultant = TcdUserConsult.objects.get(id=user_id)
    print(f"User is consultant: {consultant.username}")
except TcdUserConsult.DoesNotExist:
    try:
        staff = TcdUsers.objects.get(id=user_id)
        print(f"User is staff: {staff.username}")
    except TcdUsers.DoesNotExist:
        try:
            member = TcdAppMember.objects.get(id=user_id)
            print(f"User is member (BLOCKED): {member.username}")
        except TcdAppMember.DoesNotExist:
            print("User not found in any table")
```

#### 4. File Upload Issues

**Problem**: File upload fails with validation errors

**Common Errors**:
- File too large (>1MB for images, >5MB for documents)
- Invalid file type or extension
- Missing file in request

**Solutions**:
```bash
# Test file upload with proper validation
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "message=Test upload" \
     -F "file=@test.pdf" \
     -F "recipient_id=123" \
     -F "recipient_type=consultant" \
     http://localhost:8080/api/chat/send/

# Check file size before upload
ls -lh test.pdf  # Should be <5MB for documents, <1MB for images
```

#### 5. Room Access Issues

**Problem**: Cannot access or join chat rooms

**Solutions**:
- Verify consultant owns the room they're trying to access
- Ensure staff member provides `consultant_id` when joining rooms
- Check room_id format: `consultation_{consultant_id}`
- Verify consultant_id exists in TcdUserConsult table

**Debug Steps**:
```python
# Check room ownership
from chat.models import TcdChat
from authentication.models import TcdUserConsult

consultant_id = 123
room_messages = TcdChat.objects.filter(user_consult_id=consultant_id)
print(f"Messages in consultant {consultant_id} room: {room_messages.count()}")

# Verify consultant exists
try:
    consultant = TcdUserConsult.objects.get(id=consultant_id)
    print(f"Consultant exists: {consultant.username}")
except TcdUserConsult.DoesNotExist:
    print(f"Consultant {consultant_id} not found")
```

#### 6. Timezone Issues

**Problem**: Incorrect timestamps in responses

**Solutions**:
- Verify `TIME_ZONE = "Asia/Bangkok"` in settings.py
- Ensure `USE_TZ = True` is set
- Check that pytz is installed: `pip install pytz`

**Debug Steps**:
```python
# Check timezone configuration
from django.utils import timezone
import pytz

print("Current timezone:", timezone.get_current_timezone())
print("Bangkok time:", timezone.now().astimezone(pytz.timezone('Asia/Bangkok')))
```

### Access Control Debugging

```python
# Check user type in Django shell
from authentication.models import TcdUserConsult, TcdAppMember
from MCDC.models import TcdUsers

# Check if user is consultant
user = TcdUserConsult.objects.get(id=123)
print(f"User type: {getattr(user, 'user_type', 'Not set')}")

# Check if user is staff
user = TcdUsers.objects.get(id=789)
print(f"User type: {getattr(user, 'user_type', 'Not set')}")
```

### Logs
Check application logs for detailed error information:
```bash
tail -f logs/app.log
```

Look for these specific log messages:
- `"Access denied for user type: member"` - Member trying to access chat
- `"Chat room access validation failed"` - Invalid room access attempt
- `"User type validation failed"` - Authentication issues

## 📋 Summary and Usage Examples

### Quick Start Guide

#### For Consultants
1. **Authentication**: Login to get JWT token
2. **Create Room**: Call `/api/chat/start/` (no staff_id needed)
3. **Connect WebSocket**: Use room_id with token authentication
4. **Send Messages**: Text, images, or documents to staff
5. **View History**: Access your room history anytime

```javascript
// Complete consultant workflow
const consultantWorkflow = async () => {
    // 1. Login
    const authResponse = await fetch('/api/login/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: '<EMAIL>', password: 'pass123' })
    });
    const { access_token } = (await authResponse.json()).data;

    // 2. Start chat room
    const chatResponse = await fetch('/api/chat/start/', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${access_token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ chat_type: 'consultation', device_token: 'fcm_token' })
    });
    const { room, websocket_url } = (await chatResponse.json()).data;

    // 3. Connect WebSocket
    const ws = new WebSocket(websocket_url);
    ws.onopen = () => console.log('Connected to room:', room.room_id);

    // 4. Send message
    ws.onopen = () => {
        ws.send(JSON.stringify({
            type: 'message',
            message: 'Hello staff! I need assistance with a case.',
            user_id: getCurrentUserId(),
            user_type: 'consultant'
        }));
    };
};
```

#### For Staff
1. **Authentication**: Login to get JWT token
2. **Join Room**: Call `/api/chat/start/` with consultant_id
3. **Connect WebSocket**: Use room_id with token authentication
4. **Send Messages**: Respond to consultant in their room
5. **View History**: Access consultant's room history with user_consult_id

```javascript
// Complete staff workflow
const staffWorkflow = async (consultantId) => {
    // 1. Login
    const authResponse = await fetch('/api/login/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: '<EMAIL>', password: 'pass123' })
    });
    const { access_token } = (await authResponse.json()).data;

    // 2. Join consultant's room
    const chatResponse = await fetch('/api/chat/start/', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${access_token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            consultant_id: consultantId,
            chat_type: 'consultation',
            device_token: 'fcm_token'
        })
    });
    const { room, websocket_url } = (await chatResponse.json()).data;

    // 3. Connect WebSocket
    const ws = new WebSocket(websocket_url);

    // 4. Send response
    ws.onopen = () => {
        ws.send(JSON.stringify({
            type: 'message',
            message: 'I can help you with that case. Let me review the details.',
            user_id: getCurrentUserId(),
            user_type: 'staff'
        }));
    };

    // 5. Get chat history
    const historyResponse = await fetch(`/api/chat/history/?user_consult_id=${consultantId}`, {
        headers: { 'Authorization': `Bearer ${access_token}` }
    });
    const history = await historyResponse.json();
};
```

### Key Features Summary

| Feature | Consultants | Staff | Members |
|---------|-------------|-------|---------|
| **Create Rooms** | ✅ Own room | ❌ | ❌ |
| **Join Rooms** | ✅ Own room | ✅ Any consultant room | ❌ |
| **Send Messages** | ✅ To staff | ✅ To consultants | ❌ |
| **File Upload** | ✅ Images/Documents | ✅ Images/Documents | ❌ |
| **Chat History** | ✅ Own room | ✅ With user_consult_id | ❌ |
| **WebSocket** | ✅ Authenticated | ✅ Authenticated | ❌ |
| **Push Notifications** | ✅ FCM Support | ✅ FCM Support | ❌ |

### File Upload Specifications

| File Type | Extensions | MIME Types | Max Size | Message Type |
|-----------|------------|------------|----------|--------------|
| **Images** | .jpg, .jpeg, .png | image/jpeg, image/png | 1MB | I |
| **Documents** | .pdf, .doc, .docx, .xls, .xlsx | application/pdf, application/msword, etc. | 5MB | D |

### API Response Format

All API responses follow this consistent format:
```json
{
    "status": true,
    "data": {
        // Response data here
    },
    "error_code": null,
    "error_message": null,
    "pagination": {  // For paginated responses
        "page": 1,
        "per_page": 20,
        "total": 100,
        "has_next": true,
        "has_previous": false
    }
}
```

### WebSocket Message Format

All WebSocket messages follow this format:
```json
{
    "type": "message|typing|read_receipt|join_room|error",
    "data": {
        // Message data here
    },
    "timestamp": "2024-01-15T10:30:00+07:00"
}
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for any API changes
4. Test with all user types (consultant, staff, member)
5. Verify timezone handling for datetime fields
6. Test file upload functionality with various file types
7. Submit pull requests for review

### Development Guidelines

- **No Database Migrations**: Use existing tables only
- **Authentication**: JWT tokens with RS256 algorithm
- **File Storage**: Use UPLOAD_DIR environment variable
- **Error Handling**: Use APIResponse methods consistently
- **Access Control**: Block TcdAppMembers completely

## 📄 License

This project is part of the MCDC system and follows the same licensing terms.

---

**🔗 Quick Links:**
- [API Documentation](http://localhost:8080/api/docs/)
- [WebSocket Testing](ws://localhost:8080/ws/chat/room/consultation_123/?token=YOUR_TOKEN)
- [File Upload Endpoint](http://localhost:8080/api/chat/send/)
- [Chat History](http://localhost:8080/api/chat/history/)

**📞 Support:**
For technical support or questions about the chat system implementation, please refer to the troubleshooting section or contact the development team.
